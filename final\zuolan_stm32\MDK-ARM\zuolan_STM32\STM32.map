Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f429xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f429xx.o(.text) refers to startup_stm32f429xx.o(HEAP) for Heap_Mem
    startup_stm32f429xx.o(.text) refers to startup_stm32f429xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to fmc.o(i.MX_FMC_Init) for MX_FMC_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM7_Init) for MX_TIM7_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to cmd_to_fun.o(i.CTRL_INIT) for CTRL_INIT
    main.o(i.main) refers to adc_app.o(i.adc_tim_dma_init) for adc_tim_dma_init
    main.o(i.main) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.main) refers to fpga_interface.o(i.FPGA_Interface_Init) for FPGA_Interface_Init
    main.o(i.main) refers to da_output.o(i.DA_Init) for DA_Init
    main.o(i.main) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    main.o(i.main) refers to app_pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to my_fft.o(i.fft_init) for fft_init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to my_usart.o(i.Check_UART_Status) for Check_UART_Status
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(i.main) refers to my_usart.o(.bss) for uart1_dma_buffer
    main.o(i.main) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to my_usart.o(.data) for rxTemp2
    main.o(i.main) refers to main.o(.data) for .data
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    dac.o(i.HAL_DAC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.HAL_DAC_MspInit) refers to dac.o(.bss) for .bss
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    fmc.o(i.HAL_FMC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fmc.o(i.HAL_FMC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(i.HAL_FMC_MspInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(i.HAL_SRAM_MspDeInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspInit) refers to fmc.o(i.HAL_FMC_MspInit) for HAL_FMC_MspInit
    fmc.o(i.MX_FMC_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fmc.o(i.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(i.MX_FMC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    fmc.o(i.MX_FMC_Init) refers to fmc.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM7_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM7_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM7_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM7_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to my_usart.o(.bss) for uart1_dma_buffer
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to dac.o(.bss) for hdma_dac1
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.NMI_Handler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM7_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM7_IRQHandler) refers to tim.o(.bss) for htim7
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    ad_measure.o(i.ad_proc) refers to ad_measure.o(i.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(i.readFIFOData) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(i.setSamplingFrequency) refers to cmd_to_fun.o(i.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(i.setSamplingFrequency) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.setSamplingFrequency) for setSamplingFrequency
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(i.vpp_adc_parallel) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.readFIFOData) for readFIFOData
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.findMinMax) for findMinMax
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.data) for .data
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.bss) for .bss
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(i.DA_Apply_Settings) refers to ffixull.o(x$fpl$llufromf) for __aeabi_f2ulz
    da_output.o(i.DA_Apply_Settings) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    da_output.o(i.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_START) for DA_FPGA_START
    da_output.o(i.DA_Apply_Settings) refers to da_output.o(.bss) for .bss
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.DA_SetConfig) refers to da_output.o(.bss) for .bss
    da_output.o(i.wave_test) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.wave_test) refers to stm32f4xx_hal.o(.data) for uwTick
    da_output.o(i.wave_test) refers to da_output.o(.data) for .data
    da_output.o(i.wave_test) refers to da_output.o(.bss) for .bss
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(i.fre_measure) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad1) for fre_measure_ad1
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad2) for fre_measure_ad2
    freq_measure.o(i.freq_proc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    freq_measure.o(i.freq_proc) refers to my_usart.o(i.my_printf) for my_printf
    freq_measure.o(i.freq_proc) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.get_current_ad_frequency) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_proc) refers to my_usart.o(i.my_printf) for my_printf
    key_app.o(i.key_proc) refers to my_fft.o(i.calculate_fft_spectrum) for calculate_fft_spectrum
    key_app.o(i.key_proc) refers to my_fft.o(i.output_fft_spectrum) for output_fft_spectrum
    key_app.o(i.key_proc) refers to dac_app.o(i.dac_app_set_waveform) for dac_app_set_waveform
    key_app.o(i.key_proc) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    key_app.o(i.key_proc) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.key_proc) refers to ad_measure.o(.bss) for fifo_data1_f
    key_app.o(i.key_proc) refers to da_output.o(.bss) for da_channels
    key_app.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.set_current_ad_frequency) refers to key_app.o(.data) for .data
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to my_usart.o(i.my_printf) for my_printf
    adc_app.o(i.adc_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc_app.o(i.adc_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_task) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_task) refers to usart.o(.bss) for huart1
    adc_app.o(i.adc_task) refers to adc.o(.bss) for hadc1
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_tim_dma_init) refers to tim.o(.bss) for htim3
    adc_app.o(i.adc_tim_dma_init) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_tim_dma_init) refers to adc.o(.bss) for hadc1
    dac_app.o(i.dac_app_get_actual_frequency) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(i.dac_app_get_actual_frequency) refers to tim.o(.bss) for htim6
    dac_app.o(i.dac_app_get_amplitude) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_get_zero_based) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.dac_app_set_amplitude) for dac_app_set_amplitude
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_init) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_init) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_amplitude) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_frequency) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_waveform) refers to dac_app.o(.data) for .data
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.stop_dac_dma) for stop_dac_dma
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.generate_waveform) for generate_waveform
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(i.start_dac_dma) for start_dac_dma
    dac_app.o(i.dac_app_set_zero_based) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_sine) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    dac_app.o(i.generate_sine) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_sine) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_square) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_square) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_triangle) refers to dac_app.o(.data) for .data
    dac_app.o(i.generate_triangle) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_sine) for generate_sine
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_square) for generate_square
    dac_app.o(i.generate_waveform) refers to dac_app.o(i.generate_triangle) for generate_triangle
    dac_app.o(i.generate_waveform) refers to dac_app.o(.data) for .data
    dac_app.o(i.start_dac_dma) refers to dac_app.o(i.update_timer_frequency) for update_timer_frequency
    dac_app.o(i.start_dac_dma) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    dac_app.o(i.start_dac_dma) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    dac_app.o(i.start_dac_dma) refers to dac_app.o(.bss) for .bss
    dac_app.o(i.start_dac_dma) refers to dac.o(.bss) for hdac
    dac_app.o(i.start_dac_dma) refers to tim.o(.bss) for htim6
    dac_app.o(i.stop_dac_dma) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    dac_app.o(i.stop_dac_dma) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(i.stop_dac_dma) refers to dac.o(.bss) for hdac
    dac_app.o(i.stop_dac_dma) refers to tim.o(.bss) for htim6
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    dac_app.o(i.update_timer_frequency) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dac_app.o(i.update_timer_frequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent) for HAL_TIM_GenerateEvent
    dac_app.o(i.update_timer_frequency) refers to dac_app.o(.data) for .data
    dac_app.o(i.update_timer_frequency) refers to tim.o(.bss) for htim6
    waveform_gen.o(i.WaveformGen_Generate) refers to waveform_gen.o(i.WaveformGen_GenerateCustom) for WaveformGen_GenerateCustom
    waveform_gen.o(i.WaveformGen_Generate) refers to waveform_gen.o(.constdata) for .constdata
    waveform_gen.o(i.WaveformGen_GenerateCustom) refers to waveform_gen.o(i.WaveformGen_GenerateSine) for WaveformGen_GenerateSine
    waveform_gen.o(i.WaveformGen_GenerateCustom) refers to waveform_gen.o(i.WaveformGen_GenerateSquare) for WaveformGen_GenerateSquare
    waveform_gen.o(i.WaveformGen_GenerateCustom) refers to waveform_gen.o(i.WaveformGen_GenerateTriangle) for WaveformGen_GenerateTriangle
    waveform_gen.o(i.WaveformGen_GenerateCustom) refers to waveform_gen.o(i.WaveformGen_GenerateSawtooth) for WaveformGen_GenerateSawtooth
    waveform_gen.o(i.WaveformGen_GenerateCustom) refers to waveform_gen.o(i.WaveformGen_PostProcess) for WaveformGen_PostProcess
    waveform_gen.o(i.WaveformGen_GenerateCustom) refers to waveform_gen.o(i.WaveformGen_CalculateChecksum) for WaveformGen_CalculateChecksum
    waveform_gen.o(i.WaveformGen_GenerateSawtooth) refers to waveform_gen.o(i.ClampValue) for ClampValue
    waveform_gen.o(i.WaveformGen_GenerateSine) refers to waveform_gen.o(i.ClampValue) for ClampValue
    waveform_gen.o(i.WaveformGen_GenerateSine) refers to waveform_gen.o(.constdata) for .constdata
    waveform_gen.o(i.WaveformGen_GenerateSquare) refers to waveform_gen.o(i.ClampValue) for ClampValue
    waveform_gen.o(i.WaveformGen_GenerateTriangle) refers to waveform_gen.o(i.ClampValue) for ClampValue
    waveform_gen.o(i.WaveformGen_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    waveform_gen.o(i.WaveformGen_Init) refers to waveform_gen.o(i.WaveformGen_Generate) for WaveformGen_Generate
    waveform_gen.o(i.WaveformGen_Init) refers to waveform_gen.o(.bss) for .bss
    waveform_gen.o(i.WaveformGen_PostProcess) refers to waveform_gen.o(i.ClampValue) for ClampValue
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to _printf_dec.o(.text) for _printf_int_dec
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to _printf_pad.o(.text) for _printf_pre_padding
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to waveform_gen.o(i.WaveformGen_GetStatistics) for WaveformGen_GetStatistics
    waveform_gen.o(i.WaveformGen_PrintWaveform) refers to __2printf.o(.text) for __2printf
    waveform_gen.o(i.WaveformGen_TransferToFPGA) refers to waveform_gen.o(i.WaveformGen_Validate) for WaveformGen_Validate
    waveform_gen.o(i.WaveformGen_TransferToFPGA) refers to fpga_interface.o(i.FPGA_SetWaveform) for FPGA_SetWaveform
    waveform_gen.o(i.WaveformGen_TransferToFPGA) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    waveform_gen.o(i.WaveformGen_TransferToFPGA) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    waveform_gen.o(i.WaveformGen_Validate) refers to waveform_gen.o(i.WaveformGen_CalculateChecksum) for WaveformGen_CalculateChecksum
    fpga_interface.o(i.FPGA_CalculateFreqWord) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    fpga_interface.o(i.FPGA_CommunicationTest) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fpga_interface.o(i.FPGA_CommunicationTest) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fpga_interface.o(i.FPGA_CommunicationTest) refers to fpga_interface.o(.bss) for .bss
    fpga_interface.o(i.FPGA_ConfigureDDS) refers to fpga_interface.o(i.FPGA_SetWaveform) for FPGA_SetWaveform
    fpga_interface.o(i.FPGA_ConfigureDDS) refers to fpga_interface.o(i.FPGA_SetFrequency) for FPGA_SetFrequency
    fpga_interface.o(i.FPGA_ConfigureDDS) refers to fpga_interface.o(i.FPGA_SetPhase) for FPGA_SetPhase
    fpga_interface.o(i.FPGA_ConfigureDDS) refers to fpga_interface.o(i.FPGA_EnableDDS) for FPGA_EnableDDS
    fpga_interface.o(i.FPGA_Debug_Enable) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fpga_interface.o(i.FPGA_Debug_Enable) refers to strlen.o(.text) for strlen
    fpga_interface.o(i.FPGA_Debug_Enable) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fpga_interface.o(i.FPGA_Debug_Enable) refers to fpga_interface.o(.data) for .data
    fpga_interface.o(i.FPGA_Debug_Enable) refers to usart.o(.bss) for huart1
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_pad.o(.text) for _printf_pre_padding
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_dec.o(.text) for _printf_int_dec
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to _printf_str.o(.text) for _printf_str
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to __2snprintf.o(.text) for __2snprintf
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to strlen.o(.text) for strlen
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to fpga_interface.o(.data) for .data
    fpga_interface.o(i.FPGA_Debug_PrintRegister) refers to usart.o(.bss) for huart1
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to _printf_dec.o(.text) for _printf_int_dec
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to _printf_str.o(.text) for _printf_str
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to __2snprintf.o(.text) for __2snprintf
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to strlen.o(.text) for strlen
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to fpga_interface.o(.data) for .data
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to fpga_interface.o(.conststring) for .conststring
    fpga_interface.o(i.FPGA_Debug_PrintStatus) refers to usart.o(.bss) for huart1
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to _printf_pad.o(.text) for _printf_pre_padding
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to _printf_str.o(.text) for _printf_str
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to __2snprintf.o(.text) for __2snprintf
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to strlen.o(.text) for strlen
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to fpga_interface.o(.data) for .data
    fpga_interface.o(i.FPGA_Debug_PrintTransaction) refers to usart.o(.bss) for huart1
    fpga_interface.o(i.FPGA_Debug_Task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fpga_interface.o(i.FPGA_Debug_Task) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    fpga_interface.o(i.FPGA_Debug_Task) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fpga_interface.o(i.FPGA_Debug_Task) refers to fmc_test_simple.o(i.FMC_CompleteTest) for FMC_CompleteTest
    fpga_interface.o(i.FPGA_Debug_Task) refers to fmc_test_simple.o(i.FMC_InteractiveMode) for FMC_InteractiveMode
    fpga_interface.o(i.FPGA_Debug_Task) refers to fpga_interface.o(i.FPGA_Debug_Enable) for FPGA_Debug_Enable
    fpga_interface.o(i.FPGA_Debug_Task) refers to fpga_interface.o(i.FPGA_GetStatus) for FPGA_GetStatus
    fpga_interface.o(i.FPGA_Debug_Task) refers to fpga_interface.o(i.FPGA_Debug_PrintStatus) for FPGA_Debug_PrintStatus
    fpga_interface.o(i.FPGA_Debug_Task) refers to fpga_interface.o(.data) for .data
    fpga_interface.o(i.FPGA_Debug_Task) refers to usart.o(.bss) for huart1
    fpga_interface.o(i.FPGA_DelayUs) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    fpga_interface.o(i.FPGA_EnableDDS) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fpga_interface.o(i.FPGA_EnableDDS) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fpga_interface.o(i.FPGA_EnableDDS) refers to fpga_interface.o(.bss) for .bss
    fpga_interface.o(i.FPGA_GetStatus) refers to fpga_interface.o(.bss) for .bss
    fpga_interface.o(i.FPGA_Interface_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fpga_interface.o(i.FPGA_Interface_Init) refers to fpga_interface.o(i.FPGA_CommunicationTest) for FPGA_CommunicationTest
    fpga_interface.o(i.FPGA_Interface_Init) refers to fpga_interface.o(.bss) for .bss
    fpga_interface.o(i.FPGA_Interface_Init) refers to fmc.o(.bss) for hsram2
    fpga_interface.o(i.FPGA_ReadADC) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fpga_interface.o(i.FPGA_ReadFrequency) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fpga_interface.o(i.FPGA_ReadReg) refers to fpga_interface.o(i.FPGA_DelayUs) for FPGA_DelayUs
    fpga_interface.o(i.FPGA_ReadReg) refers to fpga_interface.o(i.FPGA_Debug_PrintTransaction) for FPGA_Debug_PrintTransaction
    fpga_interface.o(i.FPGA_ReadReg) refers to fpga_interface.o(.bss) for .bss
    fpga_interface.o(i.FPGA_SetFrequency) refers to fpga_interface.o(i.FPGA_CalculateFreqWord) for FPGA_CalculateFreqWord
    fpga_interface.o(i.FPGA_SetFrequency) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fpga_interface.o(i.FPGA_SetPhase) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fpga_interface.o(i.FPGA_SetWaveform) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fpga_interface.o(i.FPGA_SystemReset) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fpga_interface.o(i.FPGA_SystemReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fpga_interface.o(i.FPGA_SystemReset) refers to fpga_interface.o(i.FPGA_Interface_Init) for FPGA_Interface_Init
    fpga_interface.o(i.FPGA_WaitReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fpga_interface.o(i.FPGA_WaitReady) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fpga_interface.o(i.FPGA_WaitReady) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fpga_interface.o(i.FPGA_WriteReg) refers to fpga_interface.o(i.FPGA_Debug_PrintTransaction) for FPGA_Debug_PrintTransaction
    fpga_interface.o(i.FPGA_WriteReg) refers to fpga_interface.o(i.FPGA_DelayUs) for FPGA_DelayUs
    fpga_interface.o(i.FPGA_WriteReg) refers to fpga_interface.o(.bss) for .bss
    fmc_test_simple.o(i.DA_OutputTest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fmc_test_simple.o(i.DA_OutputTest) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fmc_test_simple.o(i.DA_OutputTest) refers to _printf_str.o(.text) for _printf_str
    fmc_test_simple.o(i.DA_OutputTest) refers to strcpy.o(.text) for strcpy
    fmc_test_simple.o(i.DA_OutputTest) refers to strlen.o(.text) for strlen
    fmc_test_simple.o(i.DA_OutputTest) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fmc_test_simple.o(i.DA_OutputTest) refers to da_output.o(i.DA_Init) for DA_Init
    fmc_test_simple.o(i.DA_OutputTest) refers to __2snprintf.o(.text) for __2snprintf
    fmc_test_simple.o(i.DA_OutputTest) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    fmc_test_simple.o(i.DA_OutputTest) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    fmc_test_simple.o(i.DA_OutputTest) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fmc_test_simple.o(i.DA_OutputTest) refers to usart.o(.bss) for huart1
    fmc_test_simple.o(i.DA_OutputTest) refers to fmc_test_simple.o(.constdata) for .constdata
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to _printf_pad.o(.text) for _printf_pre_padding
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to _printf_str.o(.text) for _printf_str
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to strcpy.o(.text) for strcpy
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to strlen.o(.text) for strlen
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to __2snprintf.o(.text) for __2snprintf
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to usart.o(.bss) for huart1
    fmc_test_simple.o(i.FMC_AddressMappingTest) refers to fmc_test_simple.o(.constdata) for .constdata
    fmc_test_simple.o(i.FMC_BasicTest) refers to _printf_pad.o(.text) for _printf_pre_padding
    fmc_test_simple.o(i.FMC_BasicTest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fmc_test_simple.o(i.FMC_BasicTest) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fmc_test_simple.o(i.FMC_BasicTest) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    fmc_test_simple.o(i.FMC_BasicTest) refers to strcpy.o(.text) for strcpy
    fmc_test_simple.o(i.FMC_BasicTest) refers to strlen.o(.text) for strlen
    fmc_test_simple.o(i.FMC_BasicTest) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fmc_test_simple.o(i.FMC_BasicTest) refers to fpga_interface.o(i.FPGA_Debug_Enable) for FPGA_Debug_Enable
    fmc_test_simple.o(i.FMC_BasicTest) refers to __2snprintf.o(.text) for __2snprintf
    fmc_test_simple.o(i.FMC_BasicTest) refers to fpga_interface.o(i.FPGA_WriteReg) for FPGA_WriteReg
    fmc_test_simple.o(i.FMC_BasicTest) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fmc_test_simple.o(i.FMC_BasicTest) refers to fpga_interface.o(i.FPGA_ReadReg) for FPGA_ReadReg
    fmc_test_simple.o(i.FMC_BasicTest) refers to fpga_interface.o(i.FPGA_GetStatus) for FPGA_GetStatus
    fmc_test_simple.o(i.FMC_BasicTest) refers to fpga_interface.o(i.FPGA_Debug_PrintStatus) for FPGA_Debug_PrintStatus
    fmc_test_simple.o(i.FMC_BasicTest) refers to fmc_test_simple.o(.constdata) for .constdata
    fmc_test_simple.o(i.FMC_BasicTest) refers to usart.o(.bss) for huart1
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_pad.o(.text) for _printf_pre_padding
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_dec.o(.text) for _printf_int_dec
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    fmc_test_simple.o(i.FMC_CompleteTest) refers to _printf_str.o(.text) for _printf_str
    fmc_test_simple.o(i.FMC_CompleteTest) refers to strcpy.o(.text) for strcpy
    fmc_test_simple.o(i.FMC_CompleteTest) refers to strcat.o(.text) for strcat
    fmc_test_simple.o(i.FMC_CompleteTest) refers to strlen.o(.text) for strlen
    fmc_test_simple.o(i.FMC_CompleteTest) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fmc_test_simple.o(i.FMC_CompleteTest) refers to __2snprintf.o(.text) for __2snprintf
    fmc_test_simple.o(i.FMC_CompleteTest) refers to fmc_test_simple.o(i.FMC_BasicTest) for FMC_BasicTest
    fmc_test_simple.o(i.FMC_CompleteTest) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fmc_test_simple.o(i.FMC_CompleteTest) refers to fmc_test_simple.o(i.FMC_AddressMappingTest) for FMC_AddressMappingTest
    fmc_test_simple.o(i.FMC_CompleteTest) refers to fmc_test_simple.o(i.DA_OutputTest) for DA_OutputTest
    fmc_test_simple.o(i.FMC_CompleteTest) refers to fmc_test_simple.o(i.WaveformTypeTest) for WaveformTypeTest
    fmc_test_simple.o(i.FMC_CompleteTest) refers to usart.o(.bss) for huart1
    fmc_test_simple.o(i.FMC_CompleteTest) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    fmc_test_simple.o(i.FMC_CompleteTest) refers to fmc_test_simple.o(.conststring) for .conststring
    fmc_test_simple.o(i.FMC_InteractiveMode) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fmc_test_simple.o(i.FMC_InteractiveMode) refers to strlen.o(.text) for strlen
    fmc_test_simple.o(i.FMC_InteractiveMode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fmc_test_simple.o(i.FMC_InteractiveMode) refers to fmc_test_simple.o(.conststring) for .conststring
    fmc_test_simple.o(i.FMC_InteractiveMode) refers to usart.o(.bss) for huart1
    fmc_test_simple.o(i.WaveformTypeTest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fmc_test_simple.o(i.WaveformTypeTest) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fmc_test_simple.o(i.WaveformTypeTest) refers to _printf_str.o(.text) for _printf_str
    fmc_test_simple.o(i.WaveformTypeTest) refers to strcpy.o(.text) for strcpy
    fmc_test_simple.o(i.WaveformTypeTest) refers to strlen.o(.text) for strlen
    fmc_test_simple.o(i.WaveformTypeTest) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    fmc_test_simple.o(i.WaveformTypeTest) refers to __2snprintf.o(.text) for __2snprintf
    fmc_test_simple.o(i.WaveformTypeTest) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    fmc_test_simple.o(i.WaveformTypeTest) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    fmc_test_simple.o(i.WaveformTypeTest) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    fmc_test_simple.o(i.WaveformTypeTest) refers to fmc_test_simple.o(.constdata) for .constdata
    fmc_test_simple.o(i.WaveformTypeTest) refers to usart.o(.bss) for huart1
    fmc_test_simple.o(.constdata) refers to fmc_test_simple.o(.conststring) for .conststring
    my_fft.o(i.calculate_fft_spectrum) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(i.calculate_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_sinad) refers to my_fft.o(i.calculate_thd_n) for calculate_thd_n
    my_fft.o(i.calculate_sinad) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    my_fft.o(i.calculate_thd) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_thd_n) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd_n) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(i.fft_init) refers to my_fft.o(i.generate_hanning_window) for generate_hanning_window
    my_fft.o(i.fft_init) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(i.generate_hanning_window) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.get_precise_peak_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_fft_spectrum) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_fft_spectrum) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.get_precise_peak_frequency) for get_precise_peak_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd) for calculate_thd
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd_n) for calculate_thd_n
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_sinad) for calculate_sinad
    my_fft.o(i.output_fft_spectrum) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_filter.o(i.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(i.arm_fir_f32_lp) refers to my_filter.o(.constdata) for .constdata
    phase_measure.o(i.calculate_phase_diff) refers to phase_measure.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman) refers to kalman.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(.bss) for .bss
    kalman.o(i.kalman_thd) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman_thd) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman_thd) refers to kalman.o(.data) for .data
    kalman.o(i.kalman_thd) refers to kalman.o(.bss) for .bss
    wave_recognition.o(i.calculate_carrier_suppression) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    wave_recognition.o(i.calculate_carrier_suppression) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    wave_recognition.o(i.calculate_carrier_suppression) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    wave_recognition.o(i.detect_symmetry) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    wave_recognition.o(i.detect_symmetry) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    wave_recognition.o(i.detect_symmetry) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    wave_recognition.o(i.find_peaks) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    wave_recognition.o(i.find_peaks) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    wave_recognition.o(i.find_peaks) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    wave_recognition.o(i.preprocess_signal) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    wave_recognition.o(i.recognize_waveform) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    wave_recognition.o(i.recognize_waveform) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    wave_recognition.o(i.recognize_waveform) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    wave_recognition.o(i.recognize_waveform) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    wave_recognition.o(i.recognize_waveform) refers to _printf_str.o(.text) for _printf_str
    wave_recognition.o(i.recognize_waveform) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    wave_recognition.o(i.recognize_waveform) refers to _printf_dec.o(.text) for _printf_int_dec
    wave_recognition.o(i.recognize_waveform) refers to strcpy.o(.text) for strcpy
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.preprocess_signal) for preprocess_signal
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.find_peaks) for find_peaks
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.calculate_bandwidth) for calculate_bandwidth
    wave_recognition.o(i.recognize_waveform) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    wave_recognition.o(i.recognize_waveform) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    wave_recognition.o(i.recognize_waveform) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    wave_recognition.o(i.recognize_waveform) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    wave_recognition.o(i.recognize_waveform) refers to __2sprintf.o(.text) for __2sprintf
    wave_recognition.o(i.recognize_waveform) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.calculate_carrier_suppression) for calculate_carrier_suppression
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(i.detect_symmetry) for detect_symmetry
    wave_recognition.o(i.recognize_waveform) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    wave_recognition.o(i.recognize_waveform) refers to wave_recognition.o(.conststring) for .conststring
    my_hmi.o(i.HMI_Send_Float) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Float) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    my_hmi.o(i.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(i.HMI_Send_Float) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    my_hmi.o(i.HMI_Send_Float) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    my_hmi.o(i.HMI_Send_Float) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    my_hmi.o(i.HMI_Send_Float) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Int) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Int) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_String) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_String) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Wave_Clear) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Wave_Clear) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hmi.o(i.HMI_Write_Wave_Low) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Low) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.Check_UART_Status) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    my_usart.o(i.Check_UART_Status) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    my_usart.o(i.Check_UART_Status) refers to _printf_dec.o(.text) for _printf_int_dec
    my_usart.o(i.Check_UART_Status) refers to strlen.o(.text) for strlen
    my_usart.o(i.Check_UART_Status) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.Check_UART_Status) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) for HAL_UART_DeInit
    my_usart.o(i.Check_UART_Status) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    my_usart.o(i.Check_UART_Status) refers to __2sprintf.o(.text) for __2sprintf
    my_usart.o(i.Check_UART_Status) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.Check_UART_Status) refers to usart.o(.bss) for huart1
    my_usart.o(i.Check_UART_Status) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr.o(.text) for __aeabi_memclr
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart3
    my_usart.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    my_usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.ParseFrame) refers to my_usart_pack.o(i.ParseDataToVariables) for ParseDataToVariables
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.SendFrame) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.data) for .data
    scheduler.o(i.circuit_learning_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.circuit_learning_task) refers to wave_recognition.o(i.recognize_waveform) for recognize_waveform
    scheduler.o(i.circuit_learning_task) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    scheduler.o(i.circuit_learning_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    scheduler.o(i.circuit_learning_task) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.circuit_learning_task) refers to scheduler.o(.data) for .data
    scheduler.o(i.circuit_learning_task) refers to scheduler.o(.bss) for .bss
    scheduler.o(i.circuit_learning_task) refers to usart.o(.bss) for huart2
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(i.start_circuit_learning) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.start_circuit_learning) refers to scheduler.o(.data) for .data
    scheduler.o(i.uart_proc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    scheduler.o(i.uart_proc) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.uart_proc) refers to ad_measure.o(.data) for vol_amp2
    scheduler.o(i.uart_proc) refers to app_pid.o(.data) for output
    scheduler.o(i.uart_proc) refers to usart.o(.bss) for huart1
    scheduler.o(.data) refers to key_app.o(i.key_proc) for key_proc
    scheduler.o(.data) refers to scheduler.o(i.circuit_learning_task) for circuit_learning_task
    scheduler.o(.data) refers to fpga_interface.o(i.FPGA_Debug_Task) for FPGA_Debug_Task
    app_pid.o(i.PID_Init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to app_pid.o(i.increment_pid_ctrl) for increment_pid_ctrl
    app_pid.o(i.Pid_Proc) refers to ad_measure.o(.data) for vol_amp2
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.data) for .data
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.bss) for .bss
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) for FLASH_OB_DisablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) for FLASH_OB_EnablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to fmc.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to fmc.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ffixull.o(x$fpl$llufromf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixull.o(x$fpl$llufromf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixull.o(x$fpl$llufromfr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixull.o(x$fpl$llufromfr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    log10f.o(i.__hardfp_log10f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to _rserrno.o(.text) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f_x.o(i.____hardfp_log10f$lsc) refers to _rserrno.o(.text) for __set_errno
    log10f_x.o(i.____hardfp_log10f$lsc) refers to log10f_x.o(.constdata) for .constdata
    log10f_x.o(i.____softfp_log10f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.____softfp_log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(i.__log10f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.__log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(x$fpl$frnd) for _frnd
    roundf.o(i.roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to fputc.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f429xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    _printf_char_file_locked.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file_locked.o(.text) refers to fputc.o(i._fputc$unlocked) for _fputc$unlocked
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (2 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (60 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (56 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing fmc.o(.rev16_text), (4 bytes).
    Removing fmc.o(.revsh_text), (4 bytes).
    Removing fmc.o(.rrx_text), (6 bytes).
    Removing fmc.o(i.HAL_SRAM_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(.rev16_text), (4 bytes).
    Removing ad_measure.o(.revsh_text), (4 bytes).
    Removing ad_measure.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(i.ad_proc), (24 bytes).
    Removing ad_measure.o(i.findMinMax), (48 bytes).
    Removing ad_measure.o(i.readFIFOData), (124 bytes).
    Removing ad_measure.o(i.setSamplingFrequency), (160 bytes).
    Removing ad_measure.o(i.vpp_adc_parallel), (292 bytes).
    Removing ad_measure.o(.data), (16 bytes).
    Removing da_output.o(.rev16_text), (4 bytes).
    Removing da_output.o(.revsh_text), (4 bytes).
    Removing da_output.o(.rrx_text), (6 bytes).
    Removing da_output.o(i.wave_test), (76 bytes).
    Removing da_output.o(.data), (8 bytes).
    Removing freq_measure.o(.rev16_text), (4 bytes).
    Removing freq_measure.o(.revsh_text), (4 bytes).
    Removing freq_measure.o(.rrx_text), (6 bytes).
    Removing freq_measure.o(i.fre_measure), (192 bytes).
    Removing freq_measure.o(i.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(i.fre_measure_ad2), (20 bytes).
    Removing freq_measure.o(i.freq_proc), (60 bytes).
    Removing freq_measure.o(.data), (24 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(i.set_current_ad_frequency), (12 bytes).
    Removing key_app.o(.data), (4 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing adc_app.o(i.adc_task), (152 bytes).
    Removing adc_app.o(.bss), (4096 bytes).
    Removing adc_app.o(.data), (4 bytes).
    Removing dac_app.o(.rev16_text), (4 bytes).
    Removing dac_app.o(.revsh_text), (4 bytes).
    Removing dac_app.o(.rrx_text), (6 bytes).
    Removing dac_app.o(i.dac_app_get_actual_frequency), (52 bytes).
    Removing dac_app.o(i.dac_app_get_amplitude), (12 bytes).
    Removing dac_app.o(i.dac_app_get_zero_based), (12 bytes).
    Removing dac_app.o(i.dac_app_init), (32 bytes).
    Removing dac_app.o(i.dac_app_set_amplitude), (104 bytes).
    Removing dac_app.o(i.dac_app_set_frequency), (32 bytes).
    Removing dac_app.o(i.dac_app_set_zero_based), (44 bytes).
    Removing waveform_gen.o(.rev16_text), (4 bytes).
    Removing waveform_gen.o(.revsh_text), (4 bytes).
    Removing waveform_gen.o(.rrx_text), (6 bytes).
    Removing waveform_gen.o(i.ClampValue), (20 bytes).
    Removing waveform_gen.o(i.WaveformGen_CalculateChecksum), (36 bytes).
    Removing waveform_gen.o(i.WaveformGen_Generate), (48 bytes).
    Removing waveform_gen.o(i.WaveformGen_GenerateCustom), (130 bytes).
    Removing waveform_gen.o(i.WaveformGen_GenerateSawtooth), (68 bytes).
    Removing waveform_gen.o(i.WaveformGen_GenerateSine), (72 bytes).
    Removing waveform_gen.o(i.WaveformGen_GenerateSquare), (58 bytes).
    Removing waveform_gen.o(i.WaveformGen_GenerateTriangle), (112 bytes).
    Removing waveform_gen.o(i.WaveformGen_GetStatistics), (62 bytes).
    Removing waveform_gen.o(i.WaveformGen_Init), (52 bytes).
    Removing waveform_gen.o(i.WaveformGen_PostProcess), (86 bytes).
    Removing waveform_gen.o(i.WaveformGen_PrintWaveform), (252 bytes).
    Removing waveform_gen.o(i.WaveformGen_TransferToFPGA), (98 bytes).
    Removing waveform_gen.o(i.WaveformGen_Validate), (42 bytes).
    Removing waveform_gen.o(.bss), (2072 bytes).
    Removing waveform_gen.o(.constdata), (288 bytes).
    Removing fpga_interface.o(.rev16_text), (4 bytes).
    Removing fpga_interface.o(.revsh_text), (4 bytes).
    Removing fpga_interface.o(.rrx_text), (6 bytes).
    Removing fpga_interface.o(i.FPGA_CalculateFreqWord), (16 bytes).
    Removing fpga_interface.o(i.FPGA_CalculatePhaseWord), (14 bytes).
    Removing fpga_interface.o(i.FPGA_ConfigureDDS), (72 bytes).
    Removing fpga_interface.o(i.FPGA_Debug_PrintRegister), (108 bytes).
    Removing fpga_interface.o(i.FPGA_EnableDDS), (92 bytes).
    Removing fpga_interface.o(i.FPGA_ReadADC), (54 bytes).
    Removing fpga_interface.o(i.FPGA_ReadFrequency), (52 bytes).
    Removing fpga_interface.o(i.FPGA_SetFrequency), (68 bytes).
    Removing fpga_interface.o(i.FPGA_SetPhase), (32 bytes).
    Removing fpga_interface.o(i.FPGA_SetWaveform), (8 bytes).
    Removing fpga_interface.o(i.FPGA_SystemReset), (26 bytes).
    Removing fpga_interface.o(i.FPGA_WaitReady), (54 bytes).
    Removing fmc_test_simple.o(.rev16_text), (4 bytes).
    Removing fmc_test_simple.o(.revsh_text), (4 bytes).
    Removing fmc_test_simple.o(.rrx_text), (6 bytes).
    Removing my_fft.o(.rev16_text), (4 bytes).
    Removing my_fft.o(.revsh_text), (4 bytes).
    Removing my_fft.o(.rrx_text), (6 bytes).
    Removing my_filter.o(.rev16_text), (4 bytes).
    Removing my_filter.o(.revsh_text), (4 bytes).
    Removing my_filter.o(.rrx_text), (6 bytes).
    Removing my_filter.o(i.arm_fir_f32_lp), (40 bytes).
    Removing my_filter.o(.constdata), (4 bytes).
    Removing my_filter.o(.constdata), (204 bytes).
    Removing phase_measure.o(.rev16_text), (4 bytes).
    Removing phase_measure.o(.revsh_text), (4 bytes).
    Removing phase_measure.o(.rrx_text), (6 bytes).
    Removing phase_measure.o(i.calculate_phase_diff), (208 bytes).
    Removing phase_measure.o(.data), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rrx_text), (6 bytes).
    Removing kalman.o(i.Kalman_init), (48 bytes).
    Removing kalman.o(i.kalman), (96 bytes).
    Removing kalman.o(i.kalman_filter), (82 bytes).
    Removing kalman.o(i.kalman_thd), (64 bytes).
    Removing kalman.o(.bss), (308 bytes).
    Removing kalman.o(.data), (4 bytes).
    Removing wave_recognition.o(.rev16_text), (4 bytes).
    Removing wave_recognition.o(.revsh_text), (4 bytes).
    Removing wave_recognition.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(.rev16_text), (4 bytes).
    Removing my_hmi.o(.revsh_text), (4 bytes).
    Removing my_hmi.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(i.HMI_Send_Float), (192 bytes).
    Removing my_hmi.o(i.HMI_Send_Int), (124 bytes).
    Removing my_hmi.o(i.HMI_Send_String), (124 bytes).
    Removing my_hmi.o(i.HMI_Wave_Clear), (124 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Fast), (184 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Low), (132 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(.bss), (128 bytes).
    Removing my_usart.o(.data), (2 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart_pack.o(.rev16_text), (4 bytes).
    Removing my_usart_pack.o(.revsh_text), (4 bytes).
    Removing my_usart_pack.o(.rrx_text), (6 bytes).
    Removing my_usart_pack.o(i.ParseDataToVariables), (160 bytes).
    Removing my_usart_pack.o(i.ParseFrame), (66 bytes).
    Removing my_usart_pack.o(i.PrepareFrame), (232 bytes).
    Removing my_usart_pack.o(i.SendFrame), (24 bytes).
    Removing my_usart_pack.o(i.SetParseTemplate), (52 bytes).
    Removing my_usart_pack.o(.bss), (52 bytes).
    Removing my_usart_pack.o(.data), (2 bytes).
    Removing cmd_to_fun.o(.rev16_text), (4 bytes).
    Removing cmd_to_fun.o(.revsh_text), (4 bytes).
    Removing cmd_to_fun.o(.rrx_text), (6 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_READ_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_READ_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE), (42 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_SET), (42 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_START), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_STOP), (30 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.start_circuit_learning), (36 bytes).
    Removing scheduler.o(i.uart_proc), (76 bytes).
    Removing scheduler.o(.data), (4 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.Pid_Proc), (84 bytes).
    Removing app_pid.o(i.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.data), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (64 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (198 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (136 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (296 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (312 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (524 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (64 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (166 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (268 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (276 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (100 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (300 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (100 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (220 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (684 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (448 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (52 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (136 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (208 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (52 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (62 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (96 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (42 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (34 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (98 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2980 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (310 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (88 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (74 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (148 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (124 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (100 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (180 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (232 bytes).
    Removing stm32f4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit), (32 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (112 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue), (54 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Start), (114 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop), (34 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (32 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (120 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (36 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (78 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (78 bytes).
    Removing stm32f4xx_ll_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_AttributeSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_CommonSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_DeInit), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC), (110 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_Init), (76 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_AttributeSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_CommonSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_IOSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_Init), (36 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus), (22 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init), (116 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand), (84 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init), (146 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable), (16 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (32 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (108 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (112 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (114 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACplt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMAError), (26 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (238 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (210 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (180 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (528 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (176 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (544 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (338 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (154 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (148 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (188 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (266 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (92 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (150 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (100 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (256 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (116 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (148 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).

808 unused section(s) (total 934399 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file_locked.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/ffixull.s                       0x00000000   Number         0  ffixull.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frnd.s                          0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f_x.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ..\MY_APP\app_pid.c                      0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\MY_APP\scheduler.c                    0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\MY_Algorithms\Src\kalman.c            0x00000000   Number         0  kalman.o ABSOLUTE
    ..\MY_Algorithms\Src\my_fft.c            0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\MY_Algorithms\Src\my_filter.c         0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\MY_Algorithms\Src\phase_measure.c     0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\MY_Algorithms\Src\wave_recognition.c  0x00000000   Number         0  wave_recognition.o ABSOLUTE
    ..\MY_Communication\Src\my_hmi.c         0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\MY_Communication\Src\my_usart.c       0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\MY_Communication\Src\my_usart_pack.c  0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\ad_measure.c  0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\adc_app.c     0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\da_output.c   0x00000000   Number         0  da_output.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\dac_app.c     0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\fpga_interface.c 0x00000000   Number         0  fpga_interface.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\key_app.c     0x00000000   Number         0  key_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\waveform_gen.c 0x00000000   Number         0  waveform_gen.o ABSOLUTE
    ..\MY_Utilities\Src\cmd_to_fun.c         0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    ..\\MY_APP\\app_pid.c                    0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\\MY_APP\\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\kalman.c         0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_fft.c         0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_filter.c      0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\phase_measure.c  0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\wave_recognition.c 0x00000000   Number         0  wave_recognition.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_hmi.c      0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart.c    0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart_pack.c 0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\ad_measure.c 0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\adc_app.c  0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\da_output.c 0x00000000   Number         0  da_output.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\dac_app.c  0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\fpga_interface.c 0x00000000   Number         0  fpga_interface.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\key_app.c  0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\waveform_gen.c 0x00000000   Number         0  waveform_gen.o ABSOLUTE
    ..\\MY_Utilities\\Src\\cmd_to_fun.c      0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    ..\\test\\fmc_test_simple.c              0x00000000   Number         0  fmc_test_simple.o ABSOLUTE
    ..\test\fmc_test_simple.c                0x00000000   Number         0  fmc_test_simple.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_cos_f32.c                            0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    arm_fir_init_f32.c                       0x00000000   Number         0  arm_fir_init_f32.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001e8   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000204   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000220   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000220   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000226   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800022c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000232   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000238   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800023e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000244   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800024e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000254   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800025a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x08000260   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000266   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x0800026c   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x08000272   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000278   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800027e   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000284   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x0800028a   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000294   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x0800029a   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002a0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002a6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002ac   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002b0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002b2   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002b6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002bc   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002c8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002c8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002c8   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002d2   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002d4   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002d6   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002d8   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002d8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002d8   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002de   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002de   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002e2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002e2   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002ea   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002ec   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002ec   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002f0   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002f8   Section       64  startup_stm32f429xx.o(.text)
    $v0                                      0x080002f8   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x08000338   Section      238  lludivv7m.o(.text)
    .text                                    0x08000428   Section        0  vsnprintf.o(.text)
    .text                                    0x0800045c   Section        0  __2sprintf.o(.text)
    .text                                    0x08000488   Section        0  __2snprintf.o(.text)
    .text                                    0x080004c0   Section        0  _printf_pad.o(.text)
    .text                                    0x0800050e   Section        0  _printf_str.o(.text)
    .text                                    0x08000560   Section        0  _printf_dec.o(.text)
    .text                                    0x080005d8   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x080005d9   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x0800066c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080007f4   Section        0  strcpy.o(.text)
    .text                                    0x0800083c   Section        0  strlen.o(.text)
    .text                                    0x0800087a   Section        0  strcat.o(.text)
    .text                                    0x08000892   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800091c   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000980   Section       68  rt_memclr.o(.text)
    .text                                    0x080009c4   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000a12   Section        0  heapauxi.o(.text)
    .text                                    0x08000a18   Section        0  _rserrno.o(.text)
    .text                                    0x08000a2e   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000a52   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000b04   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000b2c   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000b2f   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000f4c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000f4d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000f7c   Section        0  _sputc.o(.text)
    .text                                    0x08000f86   Section        0  _snputc.o(.text)
    .text                                    0x08000f96   Section        0  _printf_char.o(.text)
    .text                                    0x08000fc4   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08001080   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x080010fc   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x080010fd   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x0800116c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001174   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800117c   Section      138  lludiv10.o(.text)
    .text                                    0x08001208   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001504   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001584   Section        0  _printf_wchar.o(.text)
    .text                                    0x080015b0   Section        0  bigflt0.o(.text)
    .text                                    0x08001694   Section        0  _wcrtomb.o(.text)
    .text                                    0x080016d4   Section        8  libspace.o(.text)
    .text                                    0x080016dc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001728   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001738   Section        0  exit.o(.text)
    .text                                    0x0800174c   Section      128  strcmpv7m.o(.text)
    .text                                    0x080017cc   Section        2  use_no_semi.o(.text)
    .text                                    0x080017ce   Section        0  indicate_semi.o(.text)
    .text                                    0x080017d0   Section        0  sys_exit.o(.text)
    [Anonymous Symbol]                       0x080017dc   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x0800189a   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x080018dc   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x08001970   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08001ac4   Section        0  arm_cos_f32.o(.text.arm_cos_f32)
    [Anonymous Symbol]                       0x08001b5c   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x08001eb8   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    CL$$btod_d2e                             0x08002232   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08002270   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080022b6   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002316   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800264e   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800272a   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002754   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800277e   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_DMAConvCplt                        0x080029c2   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x080029c3   Thumb Code   116  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08002a36   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x08002a37   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08002a4c   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x08002a4d   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_IRQHandler                         0x08002a58   Section        0  stm32f4xx_it.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x08002a64   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08002a65   Thumb Code   298  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x08002b94   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CTRL_INIT                              0x08002b96   Section        0  cmd_to_fun.o(i.CTRL_INIT)
    i.Check_UART_Status                      0x08002ba0   Section        0  my_usart.o(i.Check_UART_Status)
    i.DAC_DMAConvCpltCh1                     0x08002cf0   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1)
    i.DAC_DMAConvCpltCh2                     0x08002d00   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2)
    i.DAC_DMAErrorCh1                        0x08002d10   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1)
    i.DAC_DMAErrorCh2                        0x08002d28   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2)
    i.DAC_DMAHalfConvCpltCh1                 0x08002d40   Section        0  stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1)
    i.DAC_DMAHalfConvCpltCh2                 0x08002d4a   Section        0  stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2)
    i.DA_Apply_Settings                      0x08002d54   Section        0  da_output.o(i.DA_Apply_Settings)
    i.DA_FPGA_START                          0x08002e34   Section        0  cmd_to_fun.o(i.DA_FPGA_START)
    i.DA_FPGA_STOP                           0x08002e42   Section        0  cmd_to_fun.o(i.DA_FPGA_STOP)
    i.DA_Init                                0x08002e50   Section        0  da_output.o(i.DA_Init)
    i.DA_OutputTest                          0x08002e88   Section        0  fmc_test_simple.o(i.DA_OutputTest)
    i.DA_SetConfig                           0x080030c0   Section        0  da_output.o(i.DA_SetConfig)
    i.DMA1_Stream5_IRQHandler                0x080030e0   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream0_IRQHandler                0x080030ec   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x080030f8   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08003104   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08003105   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08003138   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08003139   Thumb Code   126  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080031b6   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080031b7   Thumb Code    56  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080031ee   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x080031f0   Section        0  main.o(i.Error_Handler)
    i.FMC_AddressMappingTest                 0x080031f4   Section        0  fmc_test_simple.o(i.FMC_AddressMappingTest)
    i.FMC_BasicTest                          0x08003378   Section        0  fmc_test_simple.o(i.FMC_BasicTest)
    i.FMC_CompleteTest                       0x08003550   Section        0  fmc_test_simple.o(i.FMC_CompleteTest)
    i.FMC_InteractiveMode                    0x0800375c   Section        0  fmc_test_simple.o(i.FMC_InteractiveMode)
    i.FMC_NORSRAM_Extended_Timing_Init       0x0800378c   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    i.FMC_NORSRAM_Init                       0x080037dc   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    i.FMC_NORSRAM_Timing_Init                0x0800386c   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    i.FPGA_CommunicationTest                 0x080038d8   Section        0  fpga_interface.o(i.FPGA_CommunicationTest)
    i.FPGA_Debug_Enable                      0x0800391c   Section        0  fpga_interface.o(i.FPGA_Debug_Enable)
    i.FPGA_Debug_PrintStatus                 0x08003994   Section        0  fpga_interface.o(i.FPGA_Debug_PrintStatus)
    i.FPGA_Debug_PrintTransaction            0x08003a04   Section        0  fpga_interface.o(i.FPGA_Debug_PrintTransaction)
    i.FPGA_Debug_Task                        0x08003a8c   Section        0  fpga_interface.o(i.FPGA_Debug_Task)
    i.FPGA_DelayUs                           0x08003b30   Section        0  fpga_interface.o(i.FPGA_DelayUs)
    FPGA_DelayUs                             0x08003b31   Thumb Code    28  fpga_interface.o(i.FPGA_DelayUs)
    i.FPGA_GetStatus                         0x08003b58   Section        0  fpga_interface.o(i.FPGA_GetStatus)
    i.FPGA_Interface_Init                    0x08003b68   Section        0  fpga_interface.o(i.FPGA_Interface_Init)
    i.FPGA_ReadReg                           0x08003b9c   Section        0  fpga_interface.o(i.FPGA_ReadReg)
    i.FPGA_WriteReg                          0x08003bfc   Section        0  fpga_interface.o(i.FPGA_WriteReg)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x08003c58   Section        0  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADC_ConfigChannel                  0x08003c5c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08003de8   Section        0  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08003e04   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08003e06   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x08003e08   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08003f4a   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08003fa0   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08003fa4   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x0800404c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x080041d8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DACEx_ConvCpltCallbackCh2          0x08004248   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2)
    i.HAL_DACEx_ConvHalfCpltCallbackCh2      0x0800424a   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2)
    i.HAL_DACEx_ErrorCallbackCh2             0x0800424c   Section        0  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2)
    i.HAL_DAC_ConfigChannel                  0x0800424e   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_ConvCpltCallbackCh1            0x080042b0   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1)
    i.HAL_DAC_ConvHalfCpltCallbackCh1        0x080042b2   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1)
    i.HAL_DAC_ErrorCallbackCh1               0x080042b4   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1)
    i.HAL_DAC_Init                           0x080042b6   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x080042e0   Section        0  dac.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_Start_DMA                      0x08004380   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
    i.HAL_DAC_Stop_DMA                       0x08004488   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA)
    i.HAL_DMA_Abort                          0x080044e4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08004586   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_DeInit                         0x080045aa   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit)
    i.HAL_DMA_IRQHandler                     0x08004614   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08004800   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080048ec   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x0800495c   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_FMC_MspInit                        0x08004984   Section        0  fmc.o(i.HAL_FMC_MspInit)
    HAL_FMC_MspInit                          0x08004985   Thumb Code   138  fmc.o(i.HAL_FMC_MspInit)
    i.HAL_GPIO_DeInit                        0x08004a28   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    i.HAL_GPIO_Init                          0x08004ba0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08004dec   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GetTick                            0x08004dfc   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08004e08   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08004e18   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08004e4c   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08004e90   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_DisableIRQ                    0x08004ec0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ)
    i.HAL_NVIC_EnableIRQ                     0x08004ee4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08004f00   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08004f68   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_EnableOverDrive              0x08004f8c   Section        0  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    i.HAL_RCC_CSSCallback                    0x08005008   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    i.HAL_RCC_ClockConfig                    0x0800500c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_EnableCSS                      0x08005188   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    i.HAL_RCC_GetHCLKFreq                    0x08005194   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x080051a0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080051c0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080051e0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_NMI_IRQHandler                 0x08005250   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    i.HAL_RCC_OscConfig                      0x08005270   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x080056f2   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x08005750   Section        0  fmc.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_Config                     0x08005754   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x0800577c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x0800577e   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08005780   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x0800581a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08005878   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x080058e8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Stop                      0x08005968   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    i.HAL_TIM_ConfigClockSource              0x0800598e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_GenerateEvent                  0x08005a78   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent)
    i.HAL_TIM_IC_CaptureCallback             0x08005aa2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08005aa4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08005c00   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08005c02   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08005c04   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08005c06   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08005c08   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08005c5c   Section        0  my_usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08005cbc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_DeInit                        0x08005d2c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DeInit)
    i.HAL_UART_ErrorCallback                 0x08005d64   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08005d68   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08005fe8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspDeInit                     0x08006050   Section        0  usart.o(i.HAL_UART_MspDeInit)
    i.HAL_UART_MspInit                       0x080060d8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive                       0x0800622c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive)
    i.HAL_UART_Receive_IT                    0x080062e8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08006304   Section        0  my_usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080063cc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x080063ce   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08006486   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08006488   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x0800648c   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DAC_Init                            0x080064ec   Section        0  dac.o(i.MX_DAC_Init)
    i.MX_DMA_Init                            0x0800653c   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FMC_Init                            0x08006598   Section        0  fmc.o(i.MX_FMC_Init)
    i.MX_GPIO_Init                           0x08006608   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM3_Init                           0x080066a8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM6_Init                           0x08006710   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_TIM7_Init                           0x08006758   Section        0  tim.o(i.MX_TIM7_Init)
    i.MX_USART1_UART_Init                    0x0800679c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x080067f8   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08006830   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08006868   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800686a   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_Init                               0x08006870   Section        0  app_pid.o(i.PID_Init)
    i.PendSV_Handler                         0x080068c0   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080068c2   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080068c4   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080068c8   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08006968   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM7_IRQHandler                        0x08006978   Section        0  stm32f4xx_it.o(i.TIM7_IRQHandler)
    i.TIM_Base_SetConfig                     0x08006984   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08006a58   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08006a6e   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08006a6f   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08006a7e   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08006a7f   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08006aa4   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08006aa5   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08006acc   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08006acd   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08006ada   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08006adb   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08006b24   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08006b25   Thumb Code   144  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08006bb4   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08006bb5   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08006bd2   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006bd3   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08006c20   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08006c21   Thumb Code    26  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08006c3a   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08006c3b   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08006c56   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08006c57   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08006d20   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08006d21   Thumb Code   248  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08006e1c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08006ecc   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x08006f04   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08006f05   Thumb Code    94  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08006f62   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08006f63   Thumb Code   142  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08006ff0   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08006ffc   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08007008   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08007014   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.WaveformTypeTest                       0x08007018   Section        0  fmc_test_simple.o(i.WaveformTypeTest)
    i.__ARM_fpclassify                       0x08007230   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x08007260   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_cosf                          0x08007288   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_log10f                        0x080073d8   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_roundf                        0x08007558   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sinf                          0x080075f4   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrtf                         0x08007784   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x080077c0   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x080077d4   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x080077dc   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x080077ec   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x080077fc   Section        0  rredf.o(i.__mathlib_rredf2)
    i._is_digit                              0x08007950   Section        0  __printf_wp.o(i._is_digit)
    i.adc_tim_dma_init                       0x08007960   Section        0  adc_app.o(i.adc_tim_dma_init)
    i.calculate_bandwidth                    0x08007994   Section        0  wave_recognition.o(i.calculate_bandwidth)
    i.calculate_carrier_suppression          0x08007a1c   Section        0  wave_recognition.o(i.calculate_carrier_suppression)
    i.calculate_fft_spectrum                 0x08007aa4   Section        0  my_fft.o(i.calculate_fft_spectrum)
    i.calculate_sinad                        0x08007b68   Section        0  my_fft.o(i.calculate_sinad)
    i.calculate_thd                          0x08007ba0   Section        0  my_fft.o(i.calculate_thd)
    i.calculate_thd_n                        0x08007d0c   Section        0  my_fft.o(i.calculate_thd_n)
    i.circuit_learning_task                  0x08007dfc   Section        0  scheduler.o(i.circuit_learning_task)
    i.dac_app_set_waveform                   0x08007f10   Section        0  dac_app.o(i.dac_app_set_waveform)
    i.detect_symmetry                        0x08007f34   Section        0  wave_recognition.o(i.detect_symmetry)
    i.fft_init                               0x08007fe8   Section        0  my_fft.o(i.fft_init)
    i.find_peaks                             0x08008004   Section        0  wave_recognition.o(i.find_peaks)
    i.generate_hanning_window                0x08008100   Section        0  my_fft.o(i.generate_hanning_window)
    i.generate_sine                          0x08008160   Section        0  dac_app.o(i.generate_sine)
    generate_sine                            0x08008161   Thumb Code   202  dac_app.o(i.generate_sine)
    i.generate_square                        0x0800823c   Section        0  dac_app.o(i.generate_square)
    generate_square                          0x0800823d   Thumb Code   116  dac_app.o(i.generate_square)
    i.generate_triangle                      0x080082b8   Section        0  dac_app.o(i.generate_triangle)
    generate_triangle                        0x080082b9   Thumb Code   292  dac_app.o(i.generate_triangle)
    i.generate_waveform                      0x080083e8   Section        0  dac_app.o(i.generate_waveform)
    generate_waveform                        0x080083e9   Thumb Code    28  dac_app.o(i.generate_waveform)
    i.get_current_ad_frequency               0x08008408   Section        0  key_app.o(i.get_current_ad_frequency)
    i.get_precise_peak_frequency             0x08008414   Section        0  my_fft.o(i.get_precise_peak_frequency)
    i.key_proc                               0x080084e4   Section        0  key_app.o(i.key_proc)
    i.key_read                               0x0800867c   Section        0  key_app.o(i.key_read)
    i.main                                   0x080086c0   Section        0  main.o(i.main)
    i.my_printf                              0x0800894c   Section        0  my_usart.o(i.my_printf)
    i.output_fft_spectrum                    0x08008980   Section        0  my_fft.o(i.output_fft_spectrum)
    i.preprocess_signal                      0x08008c7c   Section        0  wave_recognition.o(i.preprocess_signal)
    preprocess_signal                        0x08008c7d   Thumb Code   162  wave_recognition.o(i.preprocess_signal)
    i.recognize_waveform                     0x08008d28   Section        0  wave_recognition.o(i.recognize_waveform)
    i.round_to_nearest_k                     0x08009054   Section        0  my_fft.o(i.round_to_nearest_k)
    i.scheduler_init                         0x08009078   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08009084   Section        0  scheduler.o(i.scheduler_run)
    i.start_dac_dma                          0x080090c4   Section        0  dac_app.o(i.start_dac_dma)
    start_dac_dma                            0x080090c5   Thumb Code    44  dac_app.o(i.start_dac_dma)
    i.stop_dac_dma                           0x080090fc   Section        0  dac_app.o(i.stop_dac_dma)
    stop_dac_dma                             0x080090fd   Thumb Code    22  dac_app.o(i.stop_dac_dma)
    i.update_timer_frequency                 0x0800911c   Section        0  dac_app.o(i.update_timer_frequency)
    update_timer_frequency                   0x0800911d   Thumb Code   334  dac_app.o(i.update_timer_frequency)
    locale$$code                             0x08009278   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080092a4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x080092d0   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x080092d0   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dcmpinf                            0x08009332   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08009332   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$dfix                               0x0800934c   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x0800934c   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x080093aa   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x080093aa   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dleqf                              0x080093d8   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x080093d8   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08009450   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08009450   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080095a4   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080095a4   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08009640   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08009640   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800964c   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800964c   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$f2d                                0x080096b8   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x080096b8   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800970e   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800970e   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800979a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800979a   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x080097a4   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x080097a4   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$frnd                               0x080097b0   Section       96  frnd.o(x$fpl$frnd)
    $v0                                      0x080097b0   Number         0  frnd.o(x$fpl$frnd)
    x$fpl$llufromf                           0x08009810   Section       92  ffixull.o(x$fpl$llufromf)
    $v0                                      0x08009810   Number         0  ffixull.o(x$fpl$llufromf)
    x$fpl$printf1                            0x0800986c   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800986c   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08009870   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08009870   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x08009874   Section      136  fmc_test_simple.o(.constdata)
    x$fpl$usenofp                            0x08009874   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080098fc   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x080098fc   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08009904   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009914   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800991c   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800991c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08009930   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08009944   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08009944   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08009958   Section       64  log10f.o(.constdata)
    logahi                                   0x08009958   Data          32  log10f.o(.constdata)
    logalo                                   0x08009978   Data          32  log10f.o(.constdata)
    .constdata                               0x08009998   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08009998   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x080099a0   Section       32  rredf.o(.constdata)
    twooverpi                                0x080099a0   Data          32  rredf.o(.constdata)
    .constdata                               0x080099c0   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080099c0   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080099d3   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080099e8   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080099e8   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08009a24   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08009a7c   Section      125  fpga_interface.o(.conststring)
    .conststring                             0x08009afc   Section      537  fmc_test_simple.o(.conststring)
    .conststring                             0x08009d18   Section       70  wave_recognition.o(.conststring)
    locale$$data                             0x08012d84   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08012d88   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08012d90   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08012d9c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08012d9e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08012d9f   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08012da0   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08012da0   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08012da4   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08012dac   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08012eb0   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        4  main.o(.data)
    last_heartbeat                           0x20000000   Data           4  main.o(.data)
    .data                                    0x20000004   Section        8  fmc.o(.data)
    FMC_Initialized                          0x20000004   Data           4  fmc.o(.data)
    FMC_DeInitialized                        0x20000008   Data           4  fmc.o(.data)
    .data                                    0x2000000c   Section       12  key_app.o(.data)
    waveform_type                            0x20000010   Data           1  key_app.o(.data)
    current_phase                            0x20000012   Data           2  key_app.o(.data)
    current_ad_freq                          0x20000014   Data           4  key_app.o(.data)
    .data                                    0x20000018   Section        1  adc_app.o(.data)
    .data                                    0x2000001c   Section       12  dac_app.o(.data)
    current_waveform                         0x2000001c   Data           1  dac_app.o(.data)
    zero_based_waveform                      0x2000001d   Data           1  dac_app.o(.data)
    current_peak_amplitude_mv                0x2000001e   Data           2  dac_app.o(.data)
    dac_amplitude_raw                        0x20000020   Data           2  dac_app.o(.data)
    current_frequency_hz                     0x20000024   Data           4  dac_app.o(.data)
    .data                                    0x20000028   Section        8  fpga_interface.o(.data)
    debug_enabled                            0x20000028   Data           1  fpga_interface.o(.data)
    debug_state                              0x20000029   Data           1  fpga_interface.o(.data)
    last_check                               0x2000002c   Data           4  fpga_interface.o(.data)
    .data                                    0x20000030   Section       10  my_usart.o(.data)
    .data                                    0x2000003c   Section       52  scheduler.o(.data)
    learning_active                          0x2000003c   Data           1  scheduler.o(.data)
    learning_step                            0x2000003d   Data           1  scheduler.o(.data)
    learning_start_time                      0x20000040   Data           4  scheduler.o(.data)
    current_freq                             0x20000044   Data           4  scheduler.o(.data)
    step_start_time                          0x20000048   Data           4  scheduler.o(.data)
    scheduler_task                           0x2000004c   Data          36  scheduler.o(.data)
    .data                                    0x20000070   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000007c   Section        4  system_stm32f4xx.o(.data)
    .bss                                     0x20000080   Section      168  adc.o(.bss)
    .bss                                     0x20000128   Section      116  dac.o(.bss)
    .bss                                     0x2000019c   Section       80  fmc.o(.bss)
    .bss                                     0x200001ec   Section      216  tim.o(.bss)
    .bss                                     0x200002c4   Section      312  usart.o(.bss)
    .bss                                     0x200003fc   Section    12288  ad_measure.o(.bss)
    .bss                                     0x200033fc   Section       24  da_output.o(.bss)
    .bss                                     0x20003414   Section    12288  adc_app.o(.bss)
    .bss                                     0x20006414   Section      256  dac_app.o(.bss)
    waveform_buffer                          0x20006414   Data         256  dac_app.o(.bss)
    .bss                                     0x20006514   Section       12  fpga_interface.o(.bss)
    .bss                                     0x20006520   Section    16404  my_fft.o(.bss)
    .bss                                     0x2000a534   Section      512  my_usart.o(.bss)
    .bss                                     0x2000a734   Section     4048  scheduler.o(.bss)
    spectrum_buffer                          0x2000a734   Data        2000  scheduler.o(.bss)
    adc_buffer                               0x2000af04   Data        2048  scheduler.o(.bss)
    .bss                                     0x2000b704   Section       36  app_pid.o(.bss)
    .bss                                     0x2000b728   Section       96  libspace.o(.bss)
    HEAP                                     0x2000b788   Section      512  startup_stm32f429xx.o(HEAP)
    Heap_Mem                                 0x2000b788   Data         512  startup_stm32f429xx.o(HEAP)
    STACK                                    0x2000b988   Section     1024  startup_stm32f429xx.o(STACK)
    Stack_Mem                                0x2000b988   Data        1024  startup_stm32f429xx.o(STACK)
    __initial_sp                             0x2000bd88   Data           0  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001e9   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000205   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000221   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000221   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000227   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800022d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000233   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000239   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800023f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000245   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800024f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000255   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800025b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x08000261   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000267   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x0800026d   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x08000273   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000279   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800027f   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000285   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x0800028b   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000295   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x0800029b   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002a1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002a7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002ad   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002b1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002b3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002d5   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002d9   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002d9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002d9   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002e3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002e3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002eb   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002f1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002f9   Thumb Code     8  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x08000313   Thumb Code     0  startup_stm32f429xx.o(.text)
    __user_initial_stackheap                 0x08000315   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x08000339   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000339   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000429   Thumb Code    48  vsnprintf.o(.text)
    __2sprintf                               0x0800045d   Thumb Code    38  __2sprintf.o(.text)
    __2snprintf                              0x08000489   Thumb Code    50  __2snprintf.o(.text)
    _printf_pre_padding                      0x080004c1   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080004ed   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x0800050f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000561   Thumb Code   104  _printf_dec.o(.text)
    _printf_longlong_hex                     0x080005d9   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x0800062f   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x0800064b   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000657   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x0800066d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strcpy                                   0x080007f5   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x0800083d   Thumb Code    62  strlen.o(.text)
    strcat                                   0x0800087b   Thumb Code    24  strcat.o(.text)
    __aeabi_memcpy                           0x08000893   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000893   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080008f9   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x0800091d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800091d   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800091d   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000965   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x08000981   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000981   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000985   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080009c5   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080009c5   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080009c5   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080009c9   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000a13   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x08000a15   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x08000a17   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x08000a19   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000a23   Thumb Code    12  _rserrno.o(.text)
    _printf_truncate_signed                  0x08000a2f   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000a41   Thumb Code    18  _printf_truncate.o(.text)
    _printf_int_common                       0x08000a53   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x08000b05   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x08000b2d   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000cdf   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000f57   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000f7d   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000f87   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000f97   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000fab   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000fbb   Thumb Code     8  _printf_char.o(.text)
    _printf_wctomb                           0x08000fc5   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08001081   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080010fd   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x0800113f   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08001157   Thumb Code    12  _printf_oct_int_ll.o(.text)
    __rt_locale                              0x0800116d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08001175   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08001175   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08001175   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x0800117d   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_hex_real                      0x08001209   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x08001505   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_lcs_common                       0x08001585   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001599   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080015a9   Thumb Code     8  _printf_wchar.o(.text)
    _btod_etento                             0x080015b1   Thumb Code   224  bigflt0.o(.text)
    _wcrtomb                                 0x08001695   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x080016d5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080016d5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080016d5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080016dd   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001729   Thumb Code    16  rt_ctype_table.o(.text)
    exit                                     0x08001739   Thumb Code    18  exit.o(.text)
    strcmp                                   0x0800174d   Thumb Code   128  strcmpv7m.o(.text)
    __I$use$semihosting                      0x080017cd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080017cd   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080017cf   Thumb Code     0  indicate_semi.o(.text)
    _sys_exit                                0x080017d1   Thumb Code     8  sys_exit.o(.text)
    arm_bitreversal_f32                      0x080017dd   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x0800189b   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x080018dd   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x08001971   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_cos_f32                              0x08001ac5   Thumb Code   152  arm_cos_f32.o(.text.arm_cos_f32)
    arm_radix4_butterfly_f32                 0x08001b5d   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x08001eb9   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    _btod_d2e                                0x08002233   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08002271   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080022b7   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002317   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800264f   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800272b   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002755   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800277f   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_IRQHandler                           0x08002a59   Thumb Code     6  stm32f4xx_it.o(i.ADC_IRQHandler)
    BusFault_Handler                         0x08002b95   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    CTRL_INIT                                0x08002b97   Thumb Code    10  cmd_to_fun.o(i.CTRL_INIT)
    Check_UART_Status                        0x08002ba1   Thumb Code   172  my_usart.o(i.Check_UART_Status)
    DAC_DMAConvCpltCh1                       0x08002cf1   Thumb Code    16  stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1)
    DAC_DMAConvCpltCh2                       0x08002d01   Thumb Code    16  stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2)
    DAC_DMAErrorCh1                          0x08002d11   Thumb Code    24  stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1)
    DAC_DMAErrorCh2                          0x08002d29   Thumb Code    24  stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2)
    DAC_DMAHalfConvCpltCh1                   0x08002d41   Thumb Code    10  stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1)
    DAC_DMAHalfConvCpltCh2                   0x08002d4b   Thumb Code    10  stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2)
    DA_Apply_Settings                        0x08002d55   Thumb Code   206  da_output.o(i.DA_Apply_Settings)
    DA_FPGA_START                            0x08002e35   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_START)
    DA_FPGA_STOP                             0x08002e43   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_STOP)
    DA_Init                                  0x08002e51   Thumb Code    50  da_output.o(i.DA_Init)
    DA_OutputTest                            0x08002e89   Thumb Code   308  fmc_test_simple.o(i.DA_OutputTest)
    DA_SetConfig                             0x080030c1   Thumb Code    28  da_output.o(i.DA_SetConfig)
    DMA1_Stream5_IRQHandler                  0x080030e1   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x080030ed   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x080030f9   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x080031ef   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x080031f1   Thumb Code     4  main.o(i.Error_Handler)
    FMC_AddressMappingTest                   0x080031f5   Thumb Code   176  fmc_test_simple.o(i.FMC_AddressMappingTest)
    FMC_BasicTest                            0x08003379   Thumb Code   216  fmc_test_simple.o(i.FMC_BasicTest)
    FMC_CompleteTest                         0x08003551   Thumb Code   202  fmc_test_simple.o(i.FMC_CompleteTest)
    FMC_InteractiveMode                      0x0800375d   Thumb Code    38  fmc_test_simple.o(i.FMC_InteractiveMode)
    FMC_NORSRAM_Extended_Timing_Init         0x0800378d   Thumb Code    76  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    FMC_NORSRAM_Init                         0x080037dd   Thumb Code   140  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    FMC_NORSRAM_Timing_Init                  0x0800386d   Thumb Code   106  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    FPGA_CommunicationTest                   0x080038d9   Thumb Code    62  fpga_interface.o(i.FPGA_CommunicationTest)
    FPGA_Debug_Enable                        0x0800391d   Thumb Code    48  fpga_interface.o(i.FPGA_Debug_Enable)
    FPGA_Debug_PrintStatus                   0x08003995   Thumb Code    92  fpga_interface.o(i.FPGA_Debug_PrintStatus)
    FPGA_Debug_PrintTransaction              0x08003a05   Thumb Code    58  fpga_interface.o(i.FPGA_Debug_PrintTransaction)
    FPGA_Debug_Task                          0x08003a8d   Thumb Code   136  fpga_interface.o(i.FPGA_Debug_Task)
    FPGA_GetStatus                           0x08003b59   Thumb Code    10  fpga_interface.o(i.FPGA_GetStatus)
    FPGA_Interface_Init                      0x08003b69   Thumb Code    44  fpga_interface.o(i.FPGA_Interface_Init)
    FPGA_ReadReg                             0x08003b9d   Thumb Code    82  fpga_interface.o(i.FPGA_ReadReg)
    FPGA_WriteReg                            0x08003bfd   Thumb Code    78  fpga_interface.o(i.FPGA_WriteReg)
    HAL_ADCEx_InjectedConvCpltCallback       0x08003c59   Thumb Code     2  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x08003c5d   Thumb Code   374  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08003de9   Thumb Code    20  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08003e05   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08003e07   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08003e09   Thumb Code   322  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08003f4b   Thumb Code    86  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08003fa1   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08003fa5   Thumb Code   146  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x0800404d   Thumb Code   362  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x080041d9   Thumb Code   112  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DACEx_ConvCpltCallbackCh2            0x08004249   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2)
    HAL_DACEx_ConvHalfCpltCallbackCh2        0x0800424b   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2)
    HAL_DACEx_ErrorCallbackCh2               0x0800424d   Thumb Code     2  stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2)
    HAL_DAC_ConfigChannel                    0x0800424f   Thumb Code    98  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_ConvCpltCallbackCh1              0x080042b1   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1)
    HAL_DAC_ConvHalfCpltCallbackCh1          0x080042b3   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1)
    HAL_DAC_ErrorCallbackCh1                 0x080042b5   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1)
    HAL_DAC_Init                             0x080042b7   Thumb Code    42  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x080042e1   Thumb Code   138  dac.o(i.HAL_DAC_MspInit)
    HAL_DAC_Start_DMA                        0x08004381   Thumb Code   240  stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
    HAL_DAC_Stop_DMA                         0x08004489   Thumb Code    92  stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA)
    HAL_DMA_Abort                            0x080044e5   Thumb Code   162  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08004587   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_DeInit                           0x080045ab   Thumb Code   106  stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit)
    HAL_DMA_IRQHandler                       0x08004615   Thumb Code   488  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08004801   Thumb Code   230  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080048ed   Thumb Code   112  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x0800495d   Thumb Code    34  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_DeInit                          0x08004a29   Thumb Code   358  stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    HAL_GPIO_Init                            0x08004ba1   Thumb Code   564  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08004ded   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GetTick                              0x08004dfd   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08004e09   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08004e19   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08004e4d   Thumb Code    58  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08004e91   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_DisableIRQ                      0x08004ec1   Thumb Code    36  stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ)
    HAL_NVIC_EnableIRQ                       0x08004ee5   Thumb Code    28  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08004f01   Thumb Code    98  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08004f69   Thumb Code    30  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x08004f8d   Thumb Code   110  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    HAL_RCC_CSSCallback                      0x08005009   Thumb Code     2  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    HAL_RCC_ClockConfig                      0x0800500d   Thumb Code   354  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_EnableCSS                        0x08005189   Thumb Code     8  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    HAL_RCC_GetHCLKFreq                      0x08005195   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x080051a1   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080051c1   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080051e1   Thumb Code    94  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_NMI_IRQHandler                   0x08005251   Thumb Code    24  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    HAL_RCC_OscConfig                        0x08005271   Thumb Code  1154  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x080056f3   Thumb Code    94  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08005751   Thumb Code     4  fmc.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x08005755   Thumb Code    36  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x0800577d   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800577f   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08005781   Thumb Code   154  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x0800581b   Thumb Code    92  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08005879   Thumb Code    94  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x080058e9   Thumb Code   128  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Stop                        0x08005969   Thumb Code    38  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    HAL_TIM_ConfigClockSource                0x0800598f   Thumb Code   234  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_GenerateEvent                    0x08005a79   Thumb Code    42  stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent)
    HAL_TIM_IC_CaptureCallback               0x08005aa3   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08005aa5   Thumb Code   348  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08005c01   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08005c03   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08005c05   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08005c07   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08005c09   Thumb Code    82  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08005c5d   Thumb Code    74  my_usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08005cbd   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_DeInit                          0x08005d2d   Thumb Code    56  stm32f4xx_hal_uart.o(i.HAL_UART_DeInit)
    HAL_UART_ErrorCallback                   0x08005d65   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08005d69   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08005fe9   Thumb Code   102  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspDeInit                       0x08006051   Thumb Code   110  usart.o(i.HAL_UART_MspDeInit)
    HAL_UART_MspInit                         0x080060d9   Thumb Code   306  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive                         0x0800622d   Thumb Code   188  stm32f4xx_hal_uart.o(i.HAL_UART_Receive)
    HAL_UART_Receive_IT                      0x080062e9   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08006305   Thumb Code   168  my_usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080063cd   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080063cf   Thumb Code   184  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08006487   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08006489   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x0800648d   Thumb Code    88  adc.o(i.MX_ADC1_Init)
    MX_DAC_Init                              0x080064ed   Thumb Code    70  dac.o(i.MX_DAC_Init)
    MX_DMA_Init                              0x0800653d   Thumb Code    88  dma.o(i.MX_DMA_Init)
    MX_FMC_Init                              0x08006599   Thumb Code   104  fmc.o(i.MX_FMC_Init)
    MX_GPIO_Init                             0x08006609   Thumb Code   148  gpio.o(i.MX_GPIO_Init)
    MX_TIM3_Init                             0x080066a9   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM6_Init                             0x08006711   Thumb Code    62  tim.o(i.MX_TIM6_Init)
    MX_TIM7_Init                             0x08006759   Thumb Code    60  tim.o(i.MX_TIM7_Init)
    MX_USART1_UART_Init                      0x0800679d   Thumb Code    78  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080067f9   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08006831   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08006869   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800686b   Thumb Code     6  stm32f4xx_it.o(i.NMI_Handler)
    PID_Init                                 0x08006871   Thumb Code    60  app_pid.o(i.PID_Init)
    PendSV_Handler                           0x080068c1   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080068c3   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080068c5   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080068c9   Thumb Code   150  main.o(i.SystemClock_Config)
    SystemInit                               0x08006969   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM7_IRQHandler                          0x08006979   Thumb Code     6  stm32f4xx_it.o(i.TIM7_IRQHandler)
    TIM_Base_SetConfig                       0x08006985   Thumb Code   190  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08006a59   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_DMA                   0x08006e1d   Thumb Code   164  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08006ecd   Thumb Code    56  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08006ff1   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08006ffd   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08007009   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08007015   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    WaveformTypeTest                         0x08007019   Thumb Code   272  fmc_test_simple.o(i.WaveformTypeTest)
    __ARM_fpclassify                         0x08007231   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x08007261   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_cosf                            0x08007289   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_log10f                          0x080073d9   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_roundf                          0x08007559   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sinf                            0x080075f5   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrtf                           0x08007785   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x080077c1   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x080077d5   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x080077dd   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x080077ed   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x080077fd   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    _is_digit                                0x08007951   Thumb Code    14  __printf_wp.o(i._is_digit)
    adc_tim_dma_init                         0x08007961   Thumb Code    34  adc_app.o(i.adc_tim_dma_init)
    calculate_bandwidth                      0x08007995   Thumb Code   132  wave_recognition.o(i.calculate_bandwidth)
    calculate_carrier_suppression            0x08007a1d   Thumb Code   118  wave_recognition.o(i.calculate_carrier_suppression)
    calculate_fft_spectrum                   0x08007aa5   Thumb Code   180  my_fft.o(i.calculate_fft_spectrum)
    calculate_sinad                          0x08007b69   Thumb Code    48  my_fft.o(i.calculate_sinad)
    calculate_thd                            0x08007ba1   Thumb Code   336  my_fft.o(i.calculate_thd)
    calculate_thd_n                          0x08007d0d   Thumb Code   210  my_fft.o(i.calculate_thd_n)
    circuit_learning_task                    0x08007dfd   Thumb Code   182  scheduler.o(i.circuit_learning_task)
    dac_app_set_waveform                     0x08007f11   Thumb Code    30  dac_app.o(i.dac_app_set_waveform)
    detect_symmetry                          0x08007f35   Thumb Code   168  wave_recognition.o(i.detect_symmetry)
    fft_init                                 0x08007fe9   Thumb Code    22  my_fft.o(i.fft_init)
    find_peaks                               0x08008005   Thumb Code   238  wave_recognition.o(i.find_peaks)
    generate_hanning_window                  0x08008101   Thumb Code    82  my_fft.o(i.generate_hanning_window)
    get_current_ad_frequency                 0x08008409   Thumb Code     8  key_app.o(i.get_current_ad_frequency)
    get_precise_peak_frequency               0x08008415   Thumb Code   192  my_fft.o(i.get_precise_peak_frequency)
    key_proc                                 0x080084e5   Thumb Code   176  key_app.o(i.key_proc)
    key_read                                 0x0800867d   Thumb Code    60  key_app.o(i.key_read)
    main                                     0x080086c1   Thumb Code   276  main.o(i.main)
    my_printf                                0x0800894d   Thumb Code    50  my_usart.o(i.my_printf)
    output_fft_spectrum                      0x08008981   Thumb Code   436  my_fft.o(i.output_fft_spectrum)
    recognize_waveform                       0x08008d29   Thumb Code   532  wave_recognition.o(i.recognize_waveform)
    round_to_nearest_k                       0x08009055   Thumb Code    32  my_fft.o(i.round_to_nearest_k)
    scheduler_init                           0x08009079   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08009085   Thumb Code    60  scheduler.o(i.scheduler_run)
    _get_lc_numeric                          0x08009279   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080092a5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x080092d1   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x080092d1   Thumb Code    98  d2f.o(x$fpl$d2f)
    __fpl_dcmp_Inf                           0x08009333   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_d2iz                             0x0800934d   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800934d   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x080093ab   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x080093ab   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_cdcmple                          0x080093d9   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x080093d9   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800943b   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08009451   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08009451   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080095a5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08009641   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800964d   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800964d   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_f2d                              0x080096b9   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080096b9   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800970f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800979b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080097a3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080097a3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x080097a5   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _frnd                                    0x080097b1   Thumb Code    96  frnd.o(x$fpl$frnd)
    __aeabi_f2ulz                            0x08009811   Thumb Code     0  ffixull.o(x$fpl$llufromf)
    _ll_ufrom_f                              0x08009811   Thumb Code    92  ffixull.o(x$fpl$llufromf)
    _printf_fp_dec                           0x0800986d   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08009871   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08009874   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08009904   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08009914   Data           8  system_stm32f4xx.o(.constdata)
    armBitRevTable                           0x08009d5e   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    sinTable_f32                             0x0800a560   Data        2052  arm_common_tables.o(.rodata.sinTable_f32)
    twiddleCoef_4096                         0x0800ad64   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x08012d64   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08012d84   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08012dad   Data           0  lc_ctype_c.o(locale$$data)
    key_val                                  0x2000000c   Data           1  key_app.o(.data)
    key_old                                  0x2000000d   Data           1  key_app.o(.data)
    key_down                                 0x2000000e   Data           1  key_app.o(.data)
    key_up                                   0x2000000f   Data           1  key_app.o(.data)
    AdcConvEnd                               0x20000018   Data           1  adc_app.o(.data)
    commandReceived2                         0x20000030   Data           1  my_usart.o(.data)
    commandReceived3                         0x20000031   Data           1  my_usart.o(.data)
    uart1_cmd_flag                           0x20000032   Data           1  my_usart.o(.data)
    rxTemp1                                  0x20000033   Data           1  my_usart.o(.data)
    rxTemp3                                  0x20000034   Data           1  my_usart.o(.data)
    rxTemp2                                  0x20000035   Data           1  my_usart.o(.data)
    rxIndex3                                 0x20000036   Data           2  my_usart.o(.data)
    rxIndex2                                 0x20000038   Data           2  my_usart.o(.data)
    task_num                                 0x2000003e   Data           1  scheduler.o(.data)
    uwTickFreq                               0x20000070   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000074   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000078   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000007c   Data           4  system_stm32f4xx.o(.data)
    hadc1                                    0x20000080   Data          72  adc.o(.bss)
    hdma_adc1                                0x200000c8   Data          96  adc.o(.bss)
    hdac                                     0x20000128   Data          20  dac.o(.bss)
    hdma_dac1                                0x2000013c   Data          96  dac.o(.bss)
    hsram2                                   0x2000019c   Data          80  fmc.o(.bss)
    htim3                                    0x200001ec   Data          72  tim.o(.bss)
    htim6                                    0x20000234   Data          72  tim.o(.bss)
    htim7                                    0x2000027c   Data          72  tim.o(.bss)
    huart1                                   0x200002c4   Data          72  usart.o(.bss)
    huart2                                   0x2000030c   Data          72  usart.o(.bss)
    huart3                                   0x20000354   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x2000039c   Data          96  usart.o(.bss)
    fifo_data1                               0x200003fc   Data        2048  ad_measure.o(.bss)
    fifo_data2                               0x20000bfc   Data        2048  ad_measure.o(.bss)
    fifo_data1_f                             0x200013fc   Data        4096  ad_measure.o(.bss)
    fifo_data2_f                             0x200023fc   Data        4096  ad_measure.o(.bss)
    da_channels                              0x200033fc   Data          24  da_output.o(.bss)
    dac_val_buffer                           0x20003414   Data        4096  adc_app.o(.bss)
    adc_val_buffer                           0x20004414   Data        8192  adc_app.o(.bss)
    fpga_status                              0x20006514   Data          12  fpga_interface.o(.bss)
    fft_instance                             0x20006520   Data          20  my_fft.o(.bss)
    fft_input_buffer                         0x20006534   Data        8192  my_fft.o(.bss)
    fft_magnitude                            0x20008534   Data        4096  my_fft.o(.bss)
    window_buffer                            0x20009534   Data        4096  my_fft.o(.bss)
    rxBuffer3                                0x2000a534   Data         128  my_usart.o(.bss)
    rxBuffer2                                0x2000a5b4   Data         128  my_usart.o(.bss)
    uart1_dma_buffer                         0x2000a634   Data         128  my_usart.o(.bss)
    uart1_cmd_buffer                         0x2000a6b4   Data         128  my_usart.o(.bss)
    PID                                      0x2000b704   Data          36  app_pid.o(.bss)
    __libspace_start                         0x2000b728   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000b788   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00012f30, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00012eb0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         5880  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         6521    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000001a   Code   RO         6523    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000202   0x08000202   0x00000002   PAD
    0x08000204   0x08000204   0x0000001c   Code   RO         6525    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000220   0x08000220   0x00000000   Code   RO         5863    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000220   0x08000220   0x00000006   Code   RO         6013    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000226   0x08000226   0x00000006   Code   RO         6014    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800022c   0x0800022c   0x00000006   Code   RO         5862    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000232   0x08000232   0x00000006   Code   RO         6017    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000238   0x08000238   0x00000006   Code   RO         6018    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800023e   0x0800023e   0x00000006   Code   RO         6019    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000244   0x08000244   0x0000000a   Code   RO         6024    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         6016    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000254   0x08000254   0x00000006   Code   RO         5860    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         5861    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000260   0x08000260   0x00000006   Code   RO         6015    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000266   0x08000266   0x00000006   Code   RO         5859    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800026c   0x0800026c   0x00000006   Code   RO         6021    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x08000272   0x08000272   0x00000006   Code   RO         6022    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000278   0x08000278   0x00000006   Code   RO         6023    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800027e   0x0800027e   0x00000006   Code   RO         6028    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000284   0x08000284   0x00000006   Code   RO         6029    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x0800028a   0x0800028a   0x0000000a   Code   RO         6025    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000294   0x08000294   0x00000006   Code   RO         6012    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         5858    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         6026    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002a6   0x080002a6   0x00000006   Code   RO         6027    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002ac   0x080002ac   0x00000004   Code   RO         6020    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         6313    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002b2   0x080002b2   0x00000004   Code   RO         6314    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         6317    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         6320    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         6322    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         6324    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000006   Code   RO         6325    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002bc   0x080002bc   0x00000000   Code   RO         6327    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002bc   0x080002bc   0x0000000c   Code   RO         6328    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         6329    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         6331    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x0000000a   Code   RO         6332    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6333    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6335    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6337    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6339    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6341    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6343    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6345    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6347    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6351    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6353    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6355    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6357    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000002   Code   RO         6358    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002d4   0x080002d4   0x00000002   Code   RO         6502    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6360    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6362    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6364    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6367    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6370    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6372    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         6375    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000002   Code   RO         6376    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         5976    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         6126    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002d8   0x080002d8   0x00000006   Code   RO         6138    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002de   0x080002de   0x00000000   Code   RO         6128    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002de   0x080002de   0x00000004   Code   RO         6129    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002e2   0x080002e2   0x00000000   Code   RO         6131    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002e2   0x080002e2   0x00000008   Code   RO         6132    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002ea   0x080002ea   0x00000002   Code   RO         6384    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002ec   0x080002ec   0x00000000   Code   RO         6445    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002ec   0x080002ec   0x00000004   Code   RO         6446    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002f0   0x080002f0   0x00000006   Code   RO         6447    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002f6   0x080002f6   0x00000002   PAD
    0x080002f8   0x080002f8   0x00000040   Code   RO            4    .text               startup_stm32f429xx.o
    0x08000338   0x08000338   0x000000ee   Code   RO         5793    .text               c_w.l(lludivv7m.o)
    0x08000426   0x08000426   0x00000002   PAD
    0x08000428   0x08000428   0x00000034   Code   RO         5795    .text               c_w.l(vsnprintf.o)
    0x0800045c   0x0800045c   0x0000002c   Code   RO         5799    .text               c_w.l(__2sprintf.o)
    0x08000488   0x08000488   0x00000038   Code   RO         5801    .text               c_w.l(__2snprintf.o)
    0x080004c0   0x080004c0   0x0000004e   Code   RO         5811    .text               c_w.l(_printf_pad.o)
    0x0800050e   0x0800050e   0x00000052   Code   RO         5813    .text               c_w.l(_printf_str.o)
    0x08000560   0x08000560   0x00000078   Code   RO         5815    .text               c_w.l(_printf_dec.o)
    0x080005d8   0x080005d8   0x00000094   Code   RO         5835    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800066c   0x0800066c   0x00000188   Code   RO         5855    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080007f4   0x080007f4   0x00000048   Code   RO         5864    .text               c_w.l(strcpy.o)
    0x0800083c   0x0800083c   0x0000003e   Code   RO         5866    .text               c_w.l(strlen.o)
    0x0800087a   0x0800087a   0x00000018   Code   RO         5868    .text               c_w.l(strcat.o)
    0x08000892   0x08000892   0x0000008a   Code   RO         5870    .text               c_w.l(rt_memcpy_v6.o)
    0x0800091c   0x0800091c   0x00000064   Code   RO         5872    .text               c_w.l(rt_memcpy_w.o)
    0x08000980   0x08000980   0x00000044   Code   RO         5874    .text               c_w.l(rt_memclr.o)
    0x080009c4   0x080009c4   0x0000004e   Code   RO         5876    .text               c_w.l(rt_memclr_w.o)
    0x08000a12   0x08000a12   0x00000006   Code   RO         5878    .text               c_w.l(heapauxi.o)
    0x08000a18   0x08000a18   0x00000016   Code   RO         5981    .text               c_w.l(_rserrno.o)
    0x08000a2e   0x08000a2e   0x00000024   Code   RO         5983    .text               c_w.l(_printf_truncate.o)
    0x08000a52   0x08000a52   0x000000b2   Code   RO         5985    .text               c_w.l(_printf_intcommon.o)
    0x08000b04   0x08000b04   0x00000028   Code   RO         5987    .text               c_w.l(_printf_charcount.o)
    0x08000b2c   0x08000b2c   0x0000041e   Code   RO         5989    .text               c_w.l(_printf_fp_dec.o)
    0x08000f4a   0x08000f4a   0x00000002   PAD
    0x08000f4c   0x08000f4c   0x00000030   Code   RO         5991    .text               c_w.l(_printf_char_common.o)
    0x08000f7c   0x08000f7c   0x0000000a   Code   RO         5993    .text               c_w.l(_sputc.o)
    0x08000f86   0x08000f86   0x00000010   Code   RO         5995    .text               c_w.l(_snputc.o)
    0x08000f96   0x08000f96   0x0000002c   Code   RO         5997    .text               c_w.l(_printf_char.o)
    0x08000fc2   0x08000fc2   0x00000002   PAD
    0x08000fc4   0x08000fc4   0x000000bc   Code   RO         6001    .text               c_w.l(_printf_wctomb.o)
    0x08001080   0x08001080   0x0000007c   Code   RO         6004    .text               c_w.l(_printf_longlong_dec.o)
    0x080010fc   0x080010fc   0x00000070   Code   RO         6010    .text               c_w.l(_printf_oct_int_ll.o)
    0x0800116c   0x0800116c   0x00000008   Code   RO         6145    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001174   0x08001174   0x00000008   Code   RO         6150    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800117c   0x0800117c   0x0000008a   Code   RO         6152    .text               c_w.l(lludiv10.o)
    0x08001206   0x08001206   0x00000002   PAD
    0x08001208   0x08001208   0x000002fc   Code   RO         6154    .text               c_w.l(_printf_fp_hex.o)
    0x08001504   0x08001504   0x00000080   Code   RO         6157    .text               c_w.l(_printf_fp_infnan.o)
    0x08001584   0x08001584   0x0000002c   Code   RO         6161    .text               c_w.l(_printf_wchar.o)
    0x080015b0   0x080015b0   0x000000e4   Code   RO         6163    .text               c_w.l(bigflt0.o)
    0x08001694   0x08001694   0x00000040   Code   RO         6202    .text               c_w.l(_wcrtomb.o)
    0x080016d4   0x080016d4   0x00000008   Code   RO         6218    .text               c_w.l(libspace.o)
    0x080016dc   0x080016dc   0x0000004a   Code   RO         6223    .text               c_w.l(sys_stackheap_outer.o)
    0x08001726   0x08001726   0x00000002   PAD
    0x08001728   0x08001728   0x00000010   Code   RO         6225    .text               c_w.l(rt_ctype_table.o)
    0x08001738   0x08001738   0x00000012   Code   RO         6296    .text               c_w.l(exit.o)
    0x0800174a   0x0800174a   0x00000002   PAD
    0x0800174c   0x0800174c   0x00000080   Code   RO         6306    .text               c_w.l(strcmpv7m.o)
    0x080017cc   0x080017cc   0x00000002   Code   RO         6381    .text               c_w.l(use_no_semi.o)
    0x080017ce   0x080017ce   0x00000000   Code   RO         6383    .text               c_w.l(indicate_semi.o)
    0x080017ce   0x080017ce   0x00000002   PAD
    0x080017d0   0x080017d0   0x0000000c   Code   RO         6442    .text               c_w.l(sys_exit.o)
    0x080017dc   0x080017dc   0x000000be   Code   RO         5658    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x0800189a   0x0800189a   0x00000040   Code   RO         5635    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x080018da   0x080018da   0x00000002   PAD
    0x080018dc   0x080018dc   0x00000094   Code   RO         5649    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08001970   0x08001970   0x00000154   Code   RO         5616    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08001ac4   0x08001ac4   0x00000098   Code   RO         5607    .text.arm_cos_f32   arm_cortexM4lf_math.lib(arm_cos_f32.o)
    0x08001b5c   0x08001b5c   0x0000035a   Code   RO         5639    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08001eb6   0x08001eb6   0x00000002   PAD
    0x08001eb8   0x08001eb8   0x0000037a   Code   RO         5637    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08002232   0x08002232   0x0000003e   Code   RO         6166    CL$$btod_d2e        c_w.l(btod.o)
    0x08002270   0x08002270   0x00000046   Code   RO         6168    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080022b6   0x080022b6   0x00000060   Code   RO         6167    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002316   0x08002316   0x00000338   Code   RO         6176    CL$$btod_div_common  c_w.l(btod.o)
    0x0800264e   0x0800264e   0x000000dc   Code   RO         6173    CL$$btod_e2e        c_w.l(btod.o)
    0x0800272a   0x0800272a   0x0000002a   Code   RO         6170    CL$$btod_ediv       c_w.l(btod.o)
    0x08002754   0x08002754   0x0000002a   Code   RO         6169    CL$$btod_emul       c_w.l(btod.o)
    0x0800277e   0x0800277e   0x00000244   Code   RO         6175    CL$$btod_mult_common  c_w.l(btod.o)
    0x080029c2   0x080029c2   0x00000074   Code   RO         2104    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x08002a36   0x08002a36   0x00000016   Code   RO         2105    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x08002a4c   0x08002a4c   0x0000000a   Code   RO         2106    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08002a56   0x08002a56   0x00000002   PAD
    0x08002a58   0x08002a58   0x0000000c   Code   RO          653    i.ADC_IRQHandler    stm32f4xx_it.o
    0x08002a64   0x08002a64   0x00000130   Code   RO         2107    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08002b94   0x08002b94   0x00000002   Code   RO          654    i.BusFault_Handler  stm32f4xx_it.o
    0x08002b96   0x08002b96   0x0000000a   Code   RO         1913    i.CTRL_INIT         cmd_to_fun.o
    0x08002ba0   0x08002ba0   0x00000150   Code   RO         1792    i.Check_UART_Status  my_usart.o
    0x08002cf0   0x08002cf0   0x00000010   Code   RO         3659    i.DAC_DMAConvCpltCh1  stm32f4xx_hal_dac.o
    0x08002d00   0x08002d00   0x00000010   Code   RO         3803    i.DAC_DMAConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08002d10   0x08002d10   0x00000018   Code   RO         3660    i.DAC_DMAErrorCh1   stm32f4xx_hal_dac.o
    0x08002d28   0x08002d28   0x00000018   Code   RO         3804    i.DAC_DMAErrorCh2   stm32f4xx_hal_dac_ex.o
    0x08002d40   0x08002d40   0x0000000a   Code   RO         3661    i.DAC_DMAHalfConvCpltCh1  stm32f4xx_hal_dac.o
    0x08002d4a   0x08002d4a   0x0000000a   Code   RO         3805    i.DAC_DMAHalfConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08002d54   0x08002d54   0x000000e0   Code   RO          856    i.DA_Apply_Settings  da_output.o
    0x08002e34   0x08002e34   0x0000000e   Code   RO         1914    i.DA_FPGA_START     cmd_to_fun.o
    0x08002e42   0x08002e42   0x0000000e   Code   RO         1915    i.DA_FPGA_STOP      cmd_to_fun.o
    0x08002e50   0x08002e50   0x00000038   Code   RO          857    i.DA_Init           da_output.o
    0x08002e88   0x08002e88   0x00000238   Code   RO         1411    i.DA_OutputTest     fmc_test_simple.o
    0x080030c0   0x080030c0   0x00000020   Code   RO          858    i.DA_SetConfig      da_output.o
    0x080030e0   0x080030e0   0x0000000c   Code   RO          655    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x080030ec   0x080030ec   0x0000000c   Code   RO          656    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x080030f8   0x080030f8   0x0000000c   Code   RO          657    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08003104   0x08003104   0x00000034   Code   RO         2943    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08003138   0x08003138   0x0000007e   Code   RO         2944    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x080031b6   0x080031b6   0x00000038   Code   RO         2945    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080031ee   0x080031ee   0x00000002   Code   RO          658    i.DebugMon_Handler  stm32f4xx_it.o
    0x080031f0   0x080031f0   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080031f4   0x080031f4   0x00000184   Code   RO         1412    i.FMC_AddressMappingTest  fmc_test_simple.o
    0x08003378   0x08003378   0x000001d8   Code   RO         1413    i.FMC_BasicTest     fmc_test_simple.o
    0x08003550   0x08003550   0x0000020c   Code   RO         1414    i.FMC_CompleteTest  fmc_test_simple.o
    0x0800375c   0x0800375c   0x00000030   Code   RO         1415    i.FMC_InteractiveMode  fmc_test_simple.o
    0x0800378c   0x0800378c   0x00000050   Code   RO         3907    i.FMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fmc.o
    0x080037dc   0x080037dc   0x00000090   Code   RO         3908    i.FMC_NORSRAM_Init  stm32f4xx_ll_fmc.o
    0x0800386c   0x0800386c   0x0000006a   Code   RO         3909    i.FMC_NORSRAM_Timing_Init  stm32f4xx_ll_fmc.o
    0x080038d6   0x080038d6   0x00000002   PAD
    0x080038d8   0x080038d8   0x00000044   Code   RO         1259    i.FPGA_CommunicationTest  fpga_interface.o
    0x0800391c   0x0800391c   0x00000078   Code   RO         1261    i.FPGA_Debug_Enable  fpga_interface.o
    0x08003994   0x08003994   0x00000070   Code   RO         1263    i.FPGA_Debug_PrintStatus  fpga_interface.o
    0x08003a04   0x08003a04   0x00000088   Code   RO         1264    i.FPGA_Debug_PrintTransaction  fpga_interface.o
    0x08003a8c   0x08003a8c   0x000000a4   Code   RO         1265    i.FPGA_Debug_Task   fpga_interface.o
    0x08003b30   0x08003b30   0x00000028   Code   RO         1266    i.FPGA_DelayUs      fpga_interface.o
    0x08003b58   0x08003b58   0x00000010   Code   RO         1268    i.FPGA_GetStatus    fpga_interface.o
    0x08003b68   0x08003b68   0x00000034   Code   RO         1269    i.FPGA_Interface_Init  fpga_interface.o
    0x08003b9c   0x08003b9c   0x00000060   Code   RO         1272    i.FPGA_ReadReg      fpga_interface.o
    0x08003bfc   0x08003bfc   0x0000005c   Code   RO         1278    i.FPGA_WriteReg     fpga_interface.o
    0x08003c58   0x08003c58   0x00000002   Code   RO         2278    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x08003c5a   0x08003c5a   0x00000002   PAD
    0x08003c5c   0x08003c5c   0x0000018c   Code   RO         2109    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08003de8   0x08003de8   0x0000001c   Code   RO         1002    i.HAL_ADC_ConvCpltCallback  adc_app.o
    0x08003e04   0x08003e04   0x00000002   Code   RO         2111    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x08003e06   0x08003e06   0x00000002   Code   RO         2113    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08003e08   0x08003e08   0x00000142   Code   RO         2117    i.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x08003f4a   0x08003f4a   0x00000056   Code   RO         2118    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08003fa0   0x08003fa0   0x00000002   Code   RO         2119    i.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x08003fa2   0x08003fa2   0x00000002   PAD
    0x08003fa4   0x08003fa4   0x000000a8   Code   RO          389    i.HAL_ADC_MspInit   adc.o
    0x0800404c   0x0800404c   0x0000018c   Code   RO         2125    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x080041d8   0x080041d8   0x00000070   Code   RO         2128    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x08004248   0x08004248   0x00000002   Code   RO         3806    i.HAL_DACEx_ConvCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800424a   0x0800424a   0x00000002   Code   RO         3807    i.HAL_DACEx_ConvHalfCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800424c   0x0800424c   0x00000002   Code   RO         3813    i.HAL_DACEx_ErrorCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x0800424e   0x0800424e   0x00000062   Code   RO         3662    i.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x080042b0   0x080042b0   0x00000002   Code   RO         3663    i.HAL_DAC_ConvCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x080042b2   0x080042b2   0x00000002   Code   RO         3664    i.HAL_DAC_ConvHalfCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x080042b4   0x080042b4   0x00000002   Code   RO         3667    i.HAL_DAC_ErrorCallbackCh1  stm32f4xx_hal_dac.o
    0x080042b6   0x080042b6   0x0000002a   Code   RO         3672    i.HAL_DAC_Init      stm32f4xx_hal_dac.o
    0x080042e0   0x080042e0   0x000000a0   Code   RO          431    i.HAL_DAC_MspInit   dac.o
    0x08004380   0x08004380   0x00000108   Code   RO         3677    i.HAL_DAC_Start_DMA  stm32f4xx_hal_dac.o
    0x08004488   0x08004488   0x0000005c   Code   RO         3679    i.HAL_DAC_Stop_DMA  stm32f4xx_hal_dac.o
    0x080044e4   0x080044e4   0x000000a2   Code   RO         2946    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08004586   0x08004586   0x00000024   Code   RO         2947    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080045aa   0x080045aa   0x0000006a   Code   RO         2948    i.HAL_DMA_DeInit    stm32f4xx_hal_dma.o
    0x08004614   0x08004614   0x000001ec   Code   RO         2951    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08004800   0x08004800   0x000000ec   Code   RO         2952    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080048ec   0x080048ec   0x00000070   Code   RO         2956    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800495c   0x0800495c   0x00000028   Code   RO         3396    i.HAL_Delay         stm32f4xx_hal.o
    0x08004984   0x08004984   0x000000a4   Code   RO          496    i.HAL_FMC_MspInit   fmc.o
    0x08004a28   0x08004a28   0x00000178   Code   RO         2836    i.HAL_GPIO_DeInit   stm32f4xx_hal_gpio.o
    0x08004ba0   0x08004ba0   0x0000024c   Code   RO         2839    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08004dec   0x08004dec   0x0000000e   Code   RO         2841    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08004dfa   0x08004dfa   0x00000002   PAD
    0x08004dfc   0x08004dfc   0x0000000c   Code   RO         3404    i.HAL_GetTick       stm32f4xx_hal.o
    0x08004e08   0x08004e08   0x00000010   Code   RO         3410    i.HAL_IncTick       stm32f4xx_hal.o
    0x08004e18   0x08004e18   0x00000034   Code   RO         3411    i.HAL_Init          stm32f4xx_hal.o
    0x08004e4c   0x08004e4c   0x00000044   Code   RO         3412    i.HAL_InitTick      stm32f4xx_hal.o
    0x08004e90   0x08004e90   0x00000030   Code   RO          777    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08004ec0   0x08004ec0   0x00000024   Code   RO         3248    i.HAL_NVIC_DisableIRQ  stm32f4xx_hal_cortex.o
    0x08004ee4   0x08004ee4   0x0000001c   Code   RO         3249    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08004f00   0x08004f00   0x00000068   Code   RO         3255    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08004f68   0x08004f68   0x00000024   Code   RO         3256    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08004f8c   0x08004f8c   0x0000007c   Code   RO         3175    i.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x08005008   0x08005008   0x00000002   Code   RO         2382    i.HAL_RCC_CSSCallback  stm32f4xx_hal_rcc.o
    0x0800500a   0x0800500a   0x00000002   PAD
    0x0800500c   0x0800500c   0x0000017c   Code   RO         2383    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08005188   0x08005188   0x0000000c   Code   RO         2386    i.HAL_RCC_EnableCSS  stm32f4xx_hal_rcc.o
    0x08005194   0x08005194   0x0000000c   Code   RO         2388    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x080051a0   0x080051a0   0x00000020   Code   RO         2390    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x080051c0   0x080051c0   0x00000020   Code   RO         2391    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x080051e0   0x080051e0   0x00000070   Code   RO         2392    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08005250   0x08005250   0x00000020   Code   RO         2394    i.HAL_RCC_NMI_IRQHandler  stm32f4xx_hal_rcc.o
    0x08005270   0x08005270   0x00000482   Code   RO         2395    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080056f2   0x080056f2   0x0000005e   Code   RO         4083    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x08005750   0x08005750   0x00000004   Code   RO          498    i.HAL_SRAM_MspInit  fmc.o
    0x08005754   0x08005754   0x00000028   Code   RO         3260    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x0800577c   0x0800577c   0x00000002   Code   RO         4931    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x0800577e   0x0800577e   0x00000002   Code   RO         4932    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08005780   0x08005780   0x0000009a   Code   RO         4950    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x0800581a   0x0800581a   0x0000005c   Code   RO         4216    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08005876   0x08005876   0x00000002   PAD
    0x08005878   0x08005878   0x00000070   Code   RO          545    i.HAL_TIM_Base_MspInit  tim.o
    0x080058e8   0x080058e8   0x00000080   Code   RO         4219    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08005968   0x08005968   0x00000026   Code   RO         4222    i.HAL_TIM_Base_Stop  stm32f4xx_hal_tim.o
    0x0800598e   0x0800598e   0x000000ea   Code   RO         4225    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08005a78   0x08005a78   0x0000002a   Code   RO         4247    i.HAL_TIM_GenerateEvent  stm32f4xx_hal_tim.o
    0x08005aa2   0x08005aa2   0x00000002   Code   RO         4250    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08005aa4   0x08005aa4   0x0000015c   Code   RO         4264    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08005c00   0x08005c00   0x00000002   Code   RO         4267    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08005c02   0x08005c02   0x00000002   Code   RO         4294    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08005c04   0x08005c04   0x00000002   Code   RO         4302    i.HAL_TIM_PeriodElapsedCallback  stm32f4xx_hal_tim.o
    0x08005c06   0x08005c06   0x00000002   Code   RO         4307    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08005c08   0x08005c08   0x00000052   Code   RO         5208    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08005c5a   0x08005c5a   0x00000002   PAD
    0x08005c5c   0x08005c5c   0x00000060   Code   RO         1793    i.HAL_UARTEx_RxEventCallback  my_usart.o
    0x08005cbc   0x08005cbc   0x00000070   Code   RO         5222    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08005d2c   0x08005d2c   0x00000038   Code   RO         5223    i.HAL_UART_DeInit   stm32f4xx_hal_uart.o
    0x08005d64   0x08005d64   0x00000002   Code   RO         5224    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08005d66   0x08005d66   0x00000002   PAD
    0x08005d68   0x08005d68   0x00000280   Code   RO         5227    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08005fe8   0x08005fe8   0x00000066   Code   RO         5228    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x0800604e   0x0800604e   0x00000002   PAD
    0x08006050   0x08006050   0x00000088   Code   RO          598    i.HAL_UART_MspDeInit  usart.o
    0x080060d8   0x080060d8   0x00000154   Code   RO          599    i.HAL_UART_MspInit  usart.o
    0x0800622c   0x0800622c   0x000000bc   Code   RO         5231    i.HAL_UART_Receive  stm32f4xx_hal_uart.o
    0x080062e8   0x080062e8   0x0000001c   Code   RO         5233    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08006304   0x08006304   0x000000c8   Code   RO         1794    i.HAL_UART_RxCpltCallback  my_usart.o
    0x080063cc   0x080063cc   0x00000002   Code   RO         5235    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x080063ce   0x080063ce   0x000000b8   Code   RO         5236    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08006486   0x08006486   0x00000002   Code   RO         5239    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08006488   0x08006488   0x00000002   Code   RO          659    i.HardFault_Handler  stm32f4xx_it.o
    0x0800648a   0x0800648a   0x00000002   PAD
    0x0800648c   0x0800648c   0x00000060   Code   RO          390    i.MX_ADC1_Init      adc.o
    0x080064ec   0x080064ec   0x00000050   Code   RO          432    i.MX_DAC_Init       dac.o
    0x0800653c   0x0800653c   0x0000005c   Code   RO          472    i.MX_DMA_Init       dma.o
    0x08006598   0x08006598   0x00000070   Code   RO          499    i.MX_FMC_Init       fmc.o
    0x08006608   0x08006608   0x000000a0   Code   RO          360    i.MX_GPIO_Init      gpio.o
    0x080066a8   0x080066a8   0x00000068   Code   RO          546    i.MX_TIM3_Init      tim.o
    0x08006710   0x08006710   0x00000048   Code   RO          547    i.MX_TIM6_Init      tim.o
    0x08006758   0x08006758   0x00000044   Code   RO          548    i.MX_TIM7_Init      tim.o
    0x0800679c   0x0800679c   0x0000005c   Code   RO          600    i.MX_USART1_UART_Init  usart.o
    0x080067f8   0x080067f8   0x00000038   Code   RO          601    i.MX_USART2_UART_Init  usart.o
    0x08006830   0x08006830   0x00000038   Code   RO          602    i.MX_USART3_UART_Init  usart.o
    0x08006868   0x08006868   0x00000002   Code   RO          660    i.MemManage_Handler  stm32f4xx_it.o
    0x0800686a   0x0800686a   0x00000006   Code   RO          661    i.NMI_Handler       stm32f4xx_it.o
    0x08006870   0x08006870   0x00000050   Code   RO         2060    i.PID_Init          app_pid.o
    0x080068c0   0x080068c0   0x00000002   Code   RO          662    i.PendSV_Handler    stm32f4xx_it.o
    0x080068c2   0x080068c2   0x00000002   Code   RO          663    i.SVC_Handler       stm32f4xx_it.o
    0x080068c4   0x080068c4   0x00000004   Code   RO          664    i.SysTick_Handler   stm32f4xx_it.o
    0x080068c8   0x080068c8   0x000000a0   Code   RO           14    i.SystemClock_Config  main.o
    0x08006968   0x08006968   0x00000010   Code   RO         5572    i.SystemInit        system_stm32f4xx.o
    0x08006978   0x08006978   0x0000000c   Code   RO          665    i.TIM7_IRQHandler   stm32f4xx_it.o
    0x08006984   0x08006984   0x000000d4   Code   RO         4309    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08006a58   0x08006a58   0x00000016   Code   RO         4320    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08006a6e   0x08006a6e   0x00000010   Code   RO         4321    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08006a7e   0x08006a7e   0x00000026   Code   RO         4327    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08006aa4   0x08006aa4   0x00000028   Code   RO         4329    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08006acc   0x08006acc   0x0000000e   Code   RO         5241    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08006ada   0x08006ada   0x0000004a   Code   RO         5242    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08006b24   0x08006b24   0x00000090   Code   RO         5243    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08006bb4   0x08006bb4   0x0000001e   Code   RO         5245    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08006bd2   0x08006bd2   0x0000004e   Code   RO         5251    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08006c20   0x08006c20   0x0000001a   Code   RO         5252    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08006c3a   0x08006c3a   0x0000001c   Code   RO         5253    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08006c56   0x08006c56   0x000000ca   Code   RO         5254    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08006d20   0x08006d20   0x000000fc   Code   RO         5255    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08006e1c   0x08006e1c   0x000000b0   Code   RO         5256    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08006ecc   0x08006ecc   0x00000038   Code   RO         5257    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08006f04   0x08006f04   0x0000005e   Code   RO         5258    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08006f62   0x08006f62   0x0000008e   Code   RO         5259    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08006ff0   0x08006ff0   0x0000000c   Code   RO          666    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08006ffc   0x08006ffc   0x0000000c   Code   RO          667    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08007008   0x08007008   0x0000000c   Code   RO          668    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08007014   0x08007014   0x00000002   Code   RO          669    i.UsageFault_Handler  stm32f4xx_it.o
    0x08007016   0x08007016   0x00000002   PAD
    0x08007018   0x08007018   0x00000218   Code   RO         1416    i.WaveformTypeTest  fmc_test_simple.o
    0x08007230   0x08007230   0x00000030   Code   RO         6089    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08007260   0x08007260   0x00000026   Code   RO         6091    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08007286   0x08007286   0x00000002   PAD
    0x08007288   0x08007288   0x00000150   Code   RO         5908    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x080073d8   0x080073d8   0x00000180   Code   RO         5920    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08007558   0x08007558   0x0000009a   Code   RO         5972    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x080075f2   0x080075f2   0x00000002   PAD
    0x080075f4   0x080075f4   0x00000190   Code   RO         5948    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x08007784   0x08007784   0x0000003a   Code   RO         5960    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x080077be   0x080077be   0x00000002   PAD
    0x080077c0   0x080077c0   0x00000014   Code   RO         6093    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x080077d4   0x080077d4   0x00000006   Code   RO         6094    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x080077da   0x080077da   0x00000002   PAD
    0x080077dc   0x080077dc   0x00000010   Code   RO         6096    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x080077ec   0x080077ec   0x00000010   Code   RO         6099    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x080077fc   0x080077fc   0x00000154   Code   RO         6110    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x08007950   0x08007950   0x0000000e   Code   RO         5848    i._is_digit         c_w.l(__printf_wp.o)
    0x0800795e   0x0800795e   0x00000002   PAD
    0x08007960   0x08007960   0x00000034   Code   RO         1004    i.adc_tim_dma_init  adc_app.o
    0x08007994   0x08007994   0x00000088   Code   RO         1665    i.calculate_bandwidth  wave_recognition.o
    0x08007a1c   0x08007a1c   0x00000088   Code   RO         1666    i.calculate_carrier_suppression  wave_recognition.o
    0x08007aa4   0x08007aa4   0x000000c4   Code   RO         1467    i.calculate_fft_spectrum  my_fft.o
    0x08007b68   0x08007b68   0x00000038   Code   RO         1468    i.calculate_sinad   my_fft.o
    0x08007ba0   0x08007ba0   0x0000016c   Code   RO         1469    i.calculate_thd     my_fft.o
    0x08007d0c   0x08007d0c   0x000000f0   Code   RO         1470    i.calculate_thd_n   my_fft.o
    0x08007dfc   0x08007dfc   0x00000114   Code   RO         1994    i.circuit_learning_task  scheduler.o
    0x08007f10   0x08007f10   0x00000024   Code   RO         1051    i.dac_app_set_waveform  dac_app.o
    0x08007f34   0x08007f34   0x000000b4   Code   RO         1667    i.detect_symmetry   wave_recognition.o
    0x08007fe8   0x08007fe8   0x0000001c   Code   RO         1471    i.fft_init          my_fft.o
    0x08008004   0x08008004   0x000000fc   Code   RO         1668    i.find_peaks        wave_recognition.o
    0x08008100   0x08008100   0x00000060   Code   RO         1472    i.generate_hanning_window  my_fft.o
    0x08008160   0x08008160   0x000000dc   Code   RO         1053    i.generate_sine     dac_app.o
    0x0800823c   0x0800823c   0x0000007c   Code   RO         1054    i.generate_square   dac_app.o
    0x080082b8   0x080082b8   0x00000130   Code   RO         1055    i.generate_triangle  dac_app.o
    0x080083e8   0x080083e8   0x00000020   Code   RO         1056    i.generate_waveform  dac_app.o
    0x08008408   0x08008408   0x0000000c   Code   RO          952    i.get_current_ad_frequency  key_app.o
    0x08008414   0x08008414   0x000000d0   Code   RO         1473    i.get_precise_peak_frequency  my_fft.o
    0x080084e4   0x080084e4   0x00000198   Code   RO          953    i.key_proc          key_app.o
    0x0800867c   0x0800867c   0x00000044   Code   RO          954    i.key_read          key_app.o
    0x080086c0   0x080086c0   0x0000028c   Code   RO           15    i.main              main.o
    0x0800894c   0x0800894c   0x00000032   Code   RO         1795    i.my_printf         my_usart.o
    0x0800897e   0x0800897e   0x00000002   PAD
    0x08008980   0x08008980   0x000002fc   Code   RO         1474    i.output_fft_spectrum  my_fft.o
    0x08008c7c   0x08008c7c   0x000000ac   Code   RO         1669    i.preprocess_signal  wave_recognition.o
    0x08008d28   0x08008d28   0x0000032c   Code   RO         1670    i.recognize_waveform  wave_recognition.o
    0x08009054   0x08009054   0x00000024   Code   RO         1475    i.round_to_nearest_k  my_fft.o
    0x08009078   0x08009078   0x0000000c   Code   RO         1995    i.scheduler_init    scheduler.o
    0x08009084   0x08009084   0x00000040   Code   RO         1996    i.scheduler_run     scheduler.o
    0x080090c4   0x080090c4   0x00000038   Code   RO         1057    i.start_dac_dma     dac_app.o
    0x080090fc   0x080090fc   0x00000020   Code   RO         1058    i.stop_dac_dma      dac_app.o
    0x0800911c   0x0800911c   0x0000015c   Code   RO         1059    i.update_timer_frequency  dac_app.o
    0x08009278   0x08009278   0x0000002c   Code   RO         6205    locale$$code        c_w.l(lc_numeric_c.o)
    0x080092a4   0x080092a4   0x0000002c   Code   RO         6426    locale$$code        c_w.l(lc_ctype_c.o)
    0x080092d0   0x080092d0   0x00000062   Code   RO         5882    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08009332   0x08009332   0x00000018   Code   RO         6044    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800934a   0x0800934a   0x00000002   PAD
    0x0800934c   0x0800934c   0x0000005e   Code   RO         5884    x$fpl$dfix          fz_wm.l(dfix.o)
    0x080093aa   0x080093aa   0x0000002e   Code   RO         5889    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x080093d8   0x080093d8   0x00000078   Code   RO         5894    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08009450   0x08009450   0x00000154   Code   RO         5896    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080095a4   0x080095a4   0x0000009c   Code   RO         6050    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08009640   0x08009640   0x0000000c   Code   RO         6052    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800964c   0x0800964c   0x0000006c   Code   RO         5898    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x080096b8   0x080096b8   0x00000056   Code   RO         5900    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800970e   0x0800970e   0x0000008c   Code   RO         6054    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800979a   0x0800979a   0x0000000a   Code   RO         6438    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080097a4   0x080097a4   0x0000000a   Code   RO         6056    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x080097ae   0x080097ae   0x00000002   PAD
    0x080097b0   0x080097b0   0x00000060   Code   RO         6058    x$fpl$frnd          fz_wm.l(frnd.o)
    0x08009810   0x08009810   0x0000005c   Code   RO         5902    x$fpl$llufromf      fz_wm.l(ffixull.o)
    0x0800986c   0x0800986c   0x00000004   Code   RO         5906    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08009870   0x08009870   0x00000004   Code   RO         6060    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08009874   0x08009874   0x00000000   Code   RO         6068    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08009874   0x08009874   0x00000088   Data   RO         1417    .constdata          fmc_test_simple.o
    0x080098fc   0x080098fc   0x00000008   Data   RO         2958    .constdata          stm32f4xx_hal_dma.o
    0x08009904   0x08009904   0x00000010   Data   RO         5573    .constdata          system_stm32f4xx.o
    0x08009914   0x08009914   0x00000008   Data   RO         5574    .constdata          system_stm32f4xx.o
    0x0800991c   0x0800991c   0x00000028   Data   RO         5836    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08009944   0x08009944   0x00000011   Data   RO         5856    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08009955   0x08009955   0x00000003   PAD
    0x08009958   0x08009958   0x00000040   Data   RO         5923    .constdata          m_wm.l(log10f.o)
    0x08009998   0x08009998   0x00000008   Data   RO         6002    .constdata          c_w.l(_printf_wctomb.o)
    0x080099a0   0x080099a0   0x00000020   Data   RO         6111    .constdata          m_wm.l(rredf.o)
    0x080099c0   0x080099c0   0x00000026   Data   RO         6155    .constdata          c_w.l(_printf_fp_hex.o)
    0x080099e6   0x080099e6   0x00000002   PAD
    0x080099e8   0x080099e8   0x00000094   Data   RO         6164    .constdata          c_w.l(bigflt0.o)
    0x08009a7c   0x08009a7c   0x0000007d   Data   RO         1280    .conststring        fpga_interface.o
    0x08009af9   0x08009af9   0x00000003   PAD
    0x08009afc   0x08009afc   0x00000219   Data   RO         1418    .conststring        fmc_test_simple.o
    0x08009d15   0x08009d15   0x00000003   PAD
    0x08009d18   0x08009d18   0x00000046   Data   RO         1671    .conststring        wave_recognition.o
    0x08009d5e   0x08009d5e   0x00000800   Data   RO         5672    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800a55e   0x0800a55e   0x00000002   PAD
    0x0800a560   0x0800a560   0x00000804   Data   RO         5784    .rodata.sinTable_f32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800ad64   0x0800ad64   0x00008000   Data   RO         5690    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08012d64   0x08012d64   0x00000020   Data   RO         6519    Region$$Table       anon$$obj.o
    0x08012d84   0x08012d84   0x0000001c   Data   RO         6204    locale$$data        c_w.l(lc_numeric_c.o)
    0x08012da0   0x08012da0   0x00000110   Data   RO         6425    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08012f30, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08012eb0, Size: 0x0000bd88, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08012eb0   0x00000004   Data   RW           18    .data               main.o
    0x20000004   0x08012eb4   0x00000008   Data   RW          501    .data               fmc.o
    0x2000000c   0x08012ebc   0x0000000c   Data   RW          956    .data               key_app.o
    0x20000018   0x08012ec8   0x00000001   Data   RW         1007    .data               adc_app.o
    0x20000019   0x08012ec9   0x00000003   PAD
    0x2000001c   0x08012ecc   0x0000000c   Data   RW         1061    .data               dac_app.o
    0x20000028   0x08012ed8   0x00000008   Data   RW         1281    .data               fpga_interface.o
    0x20000030   0x08012ee0   0x0000000a   Data   RW         1800    .data               my_usart.o
    0x2000003a   0x08012eea   0x00000002   PAD
    0x2000003c   0x08012eec   0x00000034   Data   RW         2001    .data               scheduler.o
    0x20000070   0x08012f20   0x0000000c   Data   RW         3418    .data               stm32f4xx_hal.o
    0x2000007c   0x08012f2c   0x00000004   Data   RW         5575    .data               system_stm32f4xx.o
    0x20000080        -       0x000000a8   Zero   RW          391    .bss                adc.o
    0x20000128        -       0x00000074   Zero   RW          433    .bss                dac.o
    0x2000019c        -       0x00000050   Zero   RW          500    .bss                fmc.o
    0x200001ec        -       0x000000d8   Zero   RW          549    .bss                tim.o
    0x200002c4        -       0x00000138   Zero   RW          603    .bss                usart.o
    0x200003fc        -       0x00003000   Zero   RW          806    .bss                ad_measure.o
    0x200033fc        -       0x00000018   Zero   RW          860    .bss                da_output.o
    0x20003414        -       0x00003000   Zero   RW         1005    .bss                adc_app.o
    0x20006414        -       0x00000100   Zero   RW         1060    .bss                dac_app.o
    0x20006514        -       0x0000000c   Zero   RW         1279    .bss                fpga_interface.o
    0x20006520        -       0x00004014   Zero   RW         1476    .bss                my_fft.o
    0x2000a534        -       0x00000200   Zero   RW         1797    .bss                my_usart.o
    0x2000a734        -       0x00000fd0   Zero   RW         1999    .bss                scheduler.o
    0x2000b704        -       0x00000024   Zero   RW         2063    .bss                app_pid.o
    0x2000b728        -       0x00000060   Zero   RW         6219    .bss                c_w.l(libspace.o)
    0x2000b788        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f429xx.o
    0x2000b988        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0      12288       1148   ad_measure.o
       264         30          0          0        168       1887   adc.o
        80         26          0          1      12288       2092   adc_app.o
        80         20          0          0         36        792   app_pid.o
        38          0          0          0          0       1511   cmd_to_fun.o
       312         28          0          0         24       3433   da_output.o
       240         32          0          0        116       1877   dac.o
      1152         84          0         12        256       8694   dac_app.o
        92          4          0          0          0        882   dma.o
       280         34          0          8         80       2370   fmc.o
      2536       1324        673          0          0       4629   fmc_test_simple.o
       896        258        125          8         12      10235   fpga_interface.o
       160         12          0          0          0       1079   gpio.o
       488        244          0         12          0       2413   key_app.o
       816        386          0          4          0     808000   main.o
      1988        450          0          0      16404       9526   my_fft.o
       682        218          0         10        512       5155   my_usart.o
       352        108          0         52       4048       3658   scheduler.o
        64         26        428          0       1536        872   startup_stm32f429xx.o
       188         30          0         12          0       9777   stm32f4xx_hal.o
      1770         62          0          0          0       9833   stm32f4xx_hal_adc.o
         2          0          0          0          0       1061   stm32f4xx_hal_adc_ex.o
       244         16          0          0          0      34393   stm32f4xx_hal_cortex.o
       552         24          0          0          0       7981   stm32f4xx_hal_dac.o
        56          0          0          0          0       4221   stm32f4xx_hal_dac_ex.o
      1378         18          8          0          0       8559   stm32f4xx_hal_dma.o
       978         42          0          0          0       3565   stm32f4xx_hal_gpio.o
        48          6          0          0          0        914   stm32f4xx_hal_msp.o
       124         14          0          0          0       1356   stm32f4xx_hal_pwr_ex.o
      1768        116          0          0          0       8451   stm32f4xx_hal_rcc.o
        94          0          0          0          0       1292   stm32f4xx_hal_sram.o
      1220         22          0          0          0      12388   stm32f4xx_hal_tim.o
       158          0          0          0          0       2701   stm32f4xx_hal_tim_ex.o
      2714         20          0          0          0      20777   stm32f4xx_hal_uart.o
       120         48          0          0          0       8399   stm32f4xx_it.o
       330          8          0          0          0       4604   stm32f4xx_ll_fmc.o
        16          4         24          4          0       1211   system_stm32f4xx.o
       356         44          0          0        216       3104   tim.o
       680         90          0          0        312       4155   usart.o
      1688        338         70          0          0       8842   wave_recognition.o

    ----------------------------------------------------------------------
     25030       <USER>       <GROUP>        128      48296    1027837   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          6          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      36868          0          0       6294   arm_common_tables.o
       152         12          0          0          0       1104   arm_cos_f32.o
        56          6          0          0          0         88   __2snprintf.o
        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        24          0          0          0          0         68   strcat.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
        24          0          0          0          0        116   dcmpi.o
        94          4          0          0          0        140   dfix.o
        46          0          0          0          0        116   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
        92          4          0          0          0        132   ffixull.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
        96          4          0          0          0        124   frnd.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       336         56          0          0          0        136   cosf.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        58         18          0          0          0        464   funder.o
       384         52         64          0          0        140   log10f.o
       154          0          0          0          0        140   roundf.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
        58          0          0          0          0        136   sqrtf.o

    ----------------------------------------------------------------------
     13570        <USER>      <GROUP>          0         96      32495   Library Totals
        36          0          7          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2642         32      36868          0          0      23771   arm_cortexM4lf_math.lib
      7636        282        551          0         96       4924   c_w.l
      1440         44          0          0          0       2172   fz_wm.l
      1816        206         96          0          0       1628   m_wm.l

    ----------------------------------------------------------------------
     13570        <USER>      <GROUP>          0         96      32495   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     38600       4750      38888        128      48392    1035412   Grand Totals
     38600       4750      38888        128      48392    1035412   ELF Image Totals
     38600       4750      38888        128          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                77488 (  75.67kB)
    Total RW  Size (RW Data + ZI Data)             48520 (  47.38kB)
    Total ROM Size (Code + RO Data + RW Data)      77616 (  75.80kB)

==============================================================================

